#!/usr/bin/env python3
"""
Test script for Vonage Voice Calling Integration
"""

import os
import sys
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_vonage_config():
    """Test Vonage configuration"""
    print("🔧 Testing Vonage Configuration...")
    
    required_vars = [
        'VONAGE_API_KEY',
        'VONAGE_API_SECRET', 
        'VONAGE_APPLICATION_ID'
    ]
    
    optional_vars = [
        'VONAGE_PRIVATE_KEY',
        'VONAGE_FROM_NUMBER'
    ]
    
    missing_vars = []
    
    for var in required_vars:
        value = os.getenv(var)
        if not value:
            missing_vars.append(var)
            print(f"❌ {var}: Not set")
        else:
            print(f"✅ {var}: {'*' * len(value)}")  # Hide actual values
    
    for var in optional_vars:
        value = os.getenv(var)
        if value:
            print(f"✅ {var}: {'*' * len(value)}")
        else:
            print(f"⚠️  {var}: Not set (optional)")
    
    if missing_vars:
        print(f"\n❌ Missing required variables: {', '.join(missing_vars)}")
        return False
    else:
        print("\n✅ All required Vonage configuration variables are set!")
        return True

def test_vonage_caller():
    """Test Vonage caller initialization"""
    print("\n🔧 Testing Vonage Caller Initialization...")
    
    try:
        # Add the app directory to path
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from app.services.vonage_caller import VonageCaller
        
        # Test configuration
        config = {
            'vonage_api_key': os.getenv('VONAGE_API_KEY', 'test_key'),
            'vonage_api_secret': os.getenv('VONAGE_API_SECRET', 'test_secret'),
            'vonage_application_id': os.getenv('VONAGE_APPLICATION_ID', 'test_app_id'),
            'vonage_private_key': os.getenv('VONAGE_PRIVATE_KEY'),
            'vonage_from_number': os.getenv('VONAGE_FROM_NUMBER', '+1234567890')
        }
        
        # Initialize caller
        caller = VonageCaller(
            api_key=config['vonage_api_key'],
            api_secret=config['vonage_api_secret'],
            application_id=config['vonage_application_id'],
            private_key=config['vonage_private_key'],
            from_number=config['vonage_from_number']
        )
        
        print("✅ VonageCaller initialized successfully!")
        print(f"   - API Key: {'*' * len(caller.api_key)}")
        print(f"   - Application ID: {caller.application_id}")
        print(f"   - From Number: {caller.from_number}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error initializing VonageCaller: {e}")
        return False

def test_notification_service():
    """Test notification service with Vonage"""
    print("\n🔧 Testing Notification Service with Vonage...")
    
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from app.services.notification_service import NotificationService
        
        # Test configuration
        config = {
            'voice_calling_enabled': True,
            'vonage_api_key': os.getenv('VONAGE_API_KEY', 'test_key'),
            'vonage_api_secret': os.getenv('VONAGE_API_SECRET', 'test_secret'),
            'vonage_application_id': os.getenv('VONAGE_APPLICATION_ID', 'test_app_id'),
            'vonage_private_key': os.getenv('VONAGE_PRIVATE_KEY'),
            'vonage_from_number': os.getenv('VONAGE_FROM_NUMBER', '+1234567890'),
            # WhatsApp config (mock)
            'meta_api_token': 'test_token',
            'meta_phone_number_id': 'test_phone_id',
            'team_numbers': ['+1234567890']
        }
        
        # Initialize service
        service = NotificationService(config)
        
        print("✅ NotificationService initialized successfully!")
        print(f"   - Voice calling enabled: {service._is_voice_calling_enabled()}")
        print(f"   - Vonage caller available: {service.vonage_caller is not None}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error initializing NotificationService: {e}")
        return False

def test_database_models():
    """Test database models for Vonage compatibility"""
    print("\n🔧 Testing Database Models...")
    
    try:
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from app.models import VoiceCallLog
        
        # Test model fields
        expected_fields = [
            'call_provider',
            'external_call_id', 
            'provider_config_id'
        ]
        
        for field in expected_fields:
            if hasattr(VoiceCallLog, field):
                print(f"✅ VoiceCallLog.{field}: Available")
            else:
                print(f"❌ VoiceCallLog.{field}: Missing")
                return False
        
        print("✅ All required database fields are available!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing database models: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Vonage Voice Calling Integration Test")
    print("=" * 50)
    
    tests = [
        ("Configuration", test_vonage_config),
        ("Vonage Caller", test_vonage_caller),
        ("Notification Service", test_notification_service),
        ("Database Models", test_database_models)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Vonage integration is ready.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the configuration and setup.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
