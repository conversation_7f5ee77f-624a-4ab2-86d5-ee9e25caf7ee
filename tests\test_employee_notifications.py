#!/usr/bin/env python3
"""
Test script to verify WhatsApp notifications are only sent to active employees
"""

import sys
import os
sys.path.append('.')

from app.models import EmailLog
from app.services.whatsapp_notifier import get_whatsapp_notifier
from app.worker import employees_db

def test_active_employees():
    """Test that WhatsApp notifications are only sent to active employees"""

    print("🧪 Testing WhatsApp Notifications for Active Employees")
    print("=" * 60)

    # Show current active employees
    print("\n📋 Current Active Employees:")
    active_employees = [emp for emp in employees_db if emp.get("status") == "active"]
    for emp in active_employees:
        print(f"  • {emp['name']} - {emp['phone']}")

    print(f"\n✅ Found {len(active_employees)} active employees")

    # Get database session
    from app.models import SessionLocal
    db = SessionLocal()

    try:
        # Get the most recent email
        recent_email = db.query(EmailLog).order_by(EmailLog.received_at.desc()).first()

        if not recent_email:
            print("\n❌ No emails found in database")
            return

        print(f"\n📧 Testing with recent email:")
        print(f"  Subject: {recent_email.subject}")
        print(f"  From: {recent_email.sender}")
        print(f"  Status: {recent_email.status}")

        if not recent_email.whatsapp_summary:
            print("  ⚠️  No WhatsApp summary available")
            return

        # Test the WhatsApp notifier
        config = {
            'meta_api_token': os.getenv('META_API_TOKEN', 'test-token'),
            'meta_phone_number_id': os.getenv('META_PHONE_NUMBER_ID', 'test-id'),
            'team_numbers': ['old-hardcoded-number'],  # This should be ignored now
            'api_version': 'v18.0'
        }

        print(f"\n🔧 Configuration:")
        print(f"  Meta API Token: {'✅ Set' if config['meta_api_token'] != 'test-token' else '❌ Not set (using test)'}")
        print(f"  Phone Number ID: {'✅ Set' if config['meta_phone_number_id'] != 'test-id' else '❌ Not set (using test)'}")

        # Create WhatsApp notifier
        whatsapp_notifier = get_whatsapp_notifier(config)

        # Test the _get_active_employees method
        print(f"\n🔍 Testing employee retrieval:")
        active_recipients = whatsapp_notifier._get_active_employees(db)

        print(f"  Active recipients found: {len(active_recipients)}")
        for recipient in active_recipients:
            print(f"    📱 {recipient}")

        # Verify the recipients match our active employees
        expected_phones = [emp['phone'] for emp in active_employees]

        print(f"\n✅ Verification:")
        print(f"  Expected phones: {expected_phones}")
        print(f"  Retrieved phones: {active_recipients}")

        if set(active_recipients) == set(expected_phones):
            print("  🎉 SUCCESS: WhatsApp notifier correctly retrieves only active employees!")
        else:
            print("  ❌ MISMATCH: Retrieved phones don't match active employees")

        # Show what would happen if we sent notifications
        print(f"\n📱 Notification Preview:")
        print(f"  Would send to {len(active_recipients)} recipients:")
        for i, recipient in enumerate(active_recipients, 1):
            emp_name = next((emp['name'] for emp in active_employees if emp['phone'] == recipient), 'Unknown')
            print(f"    {i}. {emp_name} ({recipient})")

        print(f"\n📝 Message Preview:")
        message = whatsapp_notifier._format_message(recent_email)
        print(f"  {message[:200]}...")

    except Exception as e:
        print(f"\n❌ Error during testing: {str(e)}")
        import traceback
        traceback.print_exc()

    finally:
        db.close()

if __name__ == "__main__":
    test_active_employees()
