<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Monitor Agent Dashboard</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Embedded CSS for immediate loading */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #2563eb;
            --primary-dark: #1d4ed8;
            --secondary-color: #64748b;
            --success-color: #10b981;
            --warning-color: #f59e0b;
            --danger-color: #ef4444;
            --info-color: #06b6d4;
            --whatsapp-color: #25d366;
            --whatsapp-dark: #128c7e;
            --bg-primary: #ffffff;
            --bg-secondary: #f8fafc;
            --bg-tertiary: #f1f5f9;
            --bg-card: #ffffff;
            --text-primary: #0f172a;
            --text-secondary: #475569;
            --text-muted: #94a3b8;
            --text-white: #ffffff;
            --border-color: #e2e8f0;
            --border-hover: #cbd5e1;
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
            --spacing-xs: 0.25rem;
            --spacing-sm: 0.5rem;
            --spacing-md: 1rem;
            --spacing-lg: 1.5rem;
            --spacing-xl: 2rem;
            --radius-sm: 0.375rem;
            --radius-md: 0.5rem;
            --radius-lg: 0.75rem;
            --transition-fast: 0.15s ease-in-out;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: var(--bg-secondary);
            color: var(--text-primary);
            line-height: 1.6;
            font-size: 14px;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--text-white);
            padding: 0;
            box-shadow: var(--shadow-lg);
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-container {
            max-width: 1400px;
            margin: 0 auto;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 var(--spacing-lg);
            height: 70px;
        }

        .nav-brand {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: 1.25rem;
            font-weight: 700;
        }

        .nav-brand i {
            font-size: 1.5rem;
            color: var(--whatsapp-color);
        }

        .nav-menu {
            display: flex;
            gap: var(--spacing-xs);
        }

        .nav-link {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-sm) var(--spacing-md);
            color: rgba(255, 255, 255, 0.8);
            text-decoration: none;
            border-radius: var(--radius-md);
            transition: var(--transition-fast);
            font-weight: 500;
            cursor: pointer;
        }

        .nav-link:hover {
            background-color: rgba(255, 255, 255, 0.1);
            color: var(--text-white);
        }

        .nav-link.active {
            background-color: rgba(255, 255, 255, 0.15);
            color: var(--text-white);
        }

        .nav-status {
            display: flex;
            align-items: center;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-xs) var(--spacing-sm);
            background-color: rgba(255, 255, 255, 0.1);
            border-radius: var(--radius-md);
            font-size: 0.875rem;
        }

        .status-indicator i {
            color: var(--success-color);
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        .main-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--spacing-xl);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .page-header {
            margin-bottom: var(--spacing-xl);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: var(--spacing-md);
        }

        .page-header h1 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }

        .page-header h1 i {
            color: var(--primary-color);
        }

        .page-header p {
            color: var(--text-secondary);
            margin-top: var(--spacing-xs);
        }

        .btn-primary {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-sm) var(--spacing-md);
            border: none;
            border-radius: var(--radius-md);
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: var(--transition-fast);
            font-size: 0.875rem;
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            color: var(--text-white);
            box-shadow: var(--shadow-sm);
        }

        .btn-primary:hover {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--spacing-lg);
            margin-bottom: var(--spacing-xl);
        }

        .stat-card {
            background: var(--bg-card);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            transition: var(--transition-fast);
            position: relative;
            overflow: hidden;
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, var(--primary-color), var(--info-color));
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5rem;
            color: var(--text-white);
            flex-shrink: 0;
        }

        .stat-icon.emails {
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
        }

        .stat-icon.whatsapp {
            background: linear-gradient(135deg, var(--whatsapp-color), var(--whatsapp-dark));
        }

        .stat-icon.replies {
            background: linear-gradient(135deg, var(--info-color), #0891b2);
        }

        .stat-icon.sms {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        }

        .stat-icon.employees {
            background: linear-gradient(135deg, var(--warning-color), #d97706);
        }

        .stat-content h3 {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-primary);
            margin-bottom: var(--spacing-xs);
        }

        .stat-content p {
            color: var(--text-secondary);
            font-weight: 500;
            margin-bottom: var(--spacing-xs);
        }

        .stat-change {
            font-size: 0.75rem;
            font-weight: 600;
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--radius-sm);
            background-color: rgba(16, 185, 129, 0.1);
            color: var(--success-color);
        }

        .welcome-message {
            background: var(--bg-card);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            text-align: center;
            margin-bottom: var(--spacing-xl);
        }

        .welcome-message h2 {
            color: var(--primary-color);
            margin-bottom: var(--spacing-md);
        }

        .welcome-message p {
            color: var(--text-secondary);
            margin-bottom: var(--spacing-lg);
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: var(--spacing-lg);
        }

        .feature-card {
            background: var(--bg-card);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            transition: var(--transition-fast);
        }

        .feature-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .feature-card h3 {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
            color: var(--text-primary);
        }

        .feature-card h3 i {
            color: var(--primary-color);
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-sm);
            color: var(--text-secondary);
        }

        .feature-list li i {
            color: var(--success-color);
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            animation: fadeIn 0.3s ease-in-out;
        }

        .modal.active {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            background: var(--bg-card);
            border-radius: var(--radius-lg);
            box-shadow: var(--shadow-lg);
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-lg);
            border-bottom: 1px solid var(--border-color);
        }

        .modal-header h3 {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin: 0;
            color: var(--text-primary);
        }

        .modal-close {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: var(--text-secondary);
            cursor: pointer;
            padding: var(--spacing-xs);
            border-radius: var(--radius-sm);
            transition: var(--transition-fast);
        }

        .modal-close:hover {
            background-color: var(--bg-tertiary);
            color: var(--text-primary);
        }

        /* Form Styles */
        .form-group {
            margin-bottom: var(--spacing-lg);
            padding: 0 var(--spacing-lg);
        }

        .form-group:first-child {
            padding-top: var(--spacing-lg);
        }

        .form-group label {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            margin-bottom: var(--spacing-sm);
            font-weight: 500;
            color: var(--text-primary);
        }

        .form-input {
            width: 100%;
            padding: var(--spacing-sm) var(--spacing-md);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            transition: var(--transition-fast);
            background-color: var(--bg-primary);
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
        }

        .form-help {
            display: block;
            margin-top: var(--spacing-xs);
            font-size: 0.75rem;
            color: var(--text-muted);
        }

        .form-actions {
            display: flex;
            gap: var(--spacing-sm);
            padding: var(--spacing-lg);
            border-top: 1px solid var(--border-color);
            justify-content: flex-end;
        }

        .btn-secondary {
            display: inline-flex;
            align-items: center;
            gap: var(--spacing-xs);
            padding: var(--spacing-sm) var(--spacing-md);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            font-weight: 500;
            text-decoration: none;
            cursor: pointer;
            transition: var(--transition-fast);
            font-size: 0.875rem;
            background: var(--bg-primary);
            color: var(--text-secondary);
        }

        .btn-secondary:hover {
            background-color: var(--bg-tertiary);
            border-color: var(--border-hover);
            color: var(--text-primary);
        }

        /* Loading Spinner */
        .loading-spinner {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: var(--spacing-sm);
            padding: var(--spacing-xl);
            color: var(--text-secondary);
        }

        .loading-spinner i {
            font-size: 1.25rem;
        }

        /* Email and Employee Cards */
        .email-card, .employee-card {
            background: var(--bg-card);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
            box-shadow: var(--shadow-sm);
            border: 1px solid var(--border-color);
            transition: var(--transition-fast);
            cursor: pointer;
        }

        .email-card:hover, .employee-card:hover {
            transform: translateY(-2px);
            box-shadow: var(--shadow-md);
        }

        .employee-card {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }

        .employee-avatar {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--text-white);
            font-weight: 600;
            font-size: 1.125rem;
        }

        .employee-info {
            flex: 1;
        }

        .employee-info h4 {
            margin: 0 0 var(--spacing-xs) 0;
            color: var(--text-primary);
        }

        .employee-phone {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            color: var(--text-secondary);
            font-size: 0.875rem;
        }

        .employee-actions {
            display: flex;
            gap: var(--spacing-xs);
        }

        .btn-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 32px;
            height: 32px;
            border: none;
            border-radius: var(--radius-sm);
            cursor: pointer;
            transition: var(--transition-fast);
            font-size: 0.875rem;
        }

        .btn-icon.edit {
            background-color: rgba(59, 130, 246, 0.1);
            color: var(--primary-color);
        }

        .btn-icon.edit:hover {
            background-color: rgba(59, 130, 246, 0.2);
        }

        .btn-icon.delete {
            background-color: rgba(239, 68, 68, 0.1);
            color: var(--danger-color);
        }

        .btn-icon.delete:hover {
            background-color: rgba(239, 68, 68, 0.2);
        }

        /* Filters */
        .filters-section {
            display: flex;
            gap: var(--spacing-sm);
            align-items: center;
        }

        .filter-select {
            padding: var(--spacing-sm) var(--spacing-md);
            border: 1px solid var(--border-color);
            border-radius: var(--radius-md);
            font-size: 0.875rem;
            background-color: var(--bg-primary);
            color: var(--text-primary);
        }

        /* Toast Notifications */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1100;
        }

        .toast {
            background: var(--bg-card);
            border-radius: var(--radius-md);
            box-shadow: var(--shadow-lg);
            padding: var(--spacing-md);
            margin-bottom: var(--spacing-sm);
            border-left: 4px solid var(--success-color);
            animation: slideIn 0.3s ease-in-out;
            max-width: 300px;
        }

        .toast.error {
            border-left-color: var(--danger-color);
        }

        .toast.warning {
            border-left-color: var(--warning-color);
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @media (max-width: 768px) {
            .nav-menu {
                display: none;
            }

            .main-content {
                padding: var(--spacing-md);
            }

            .stats-grid {
                grid-template-columns: 1fr;
            }

            .feature-grid {
                grid-template-columns: 1fr;
            }

            .modal-content {
                width: 95%;
                margin: var(--spacing-md);
            }

            .filters-section {
                flex-direction: column;
                align-items: stretch;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-envelope-open-text"></i>
                <span>Email Monitor Agent</span>
            </div>
            <div class="nav-menu">
                <a class="nav-link active" data-tab="dashboard">
                    <i class="fas fa-chart-line"></i> Dashboard
                </a>
                <a class="nav-link" data-tab="emails">
                    <i class="fas fa-inbox"></i> Email Logs
                </a>
                <a class="nav-link" data-tab="employees">
                    <i class="fas fa-users"></i> Employees
                </a>
                <a class="nav-link" data-tab="settings">
                    <i class="fas fa-cog"></i> Settings
                </a>
            </div>
            <div class="nav-status">
                <div class="status-indicator" id="agentStatus">
                    <i class="fas fa-circle"></i>
                    <span>Agent Online</span>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Dashboard Tab -->
        <div id="dashboard" class="tab-content active">
            <div class="page-header">
                <div>
                    <h1><i class="fas fa-chart-line"></i> Dashboard</h1>
                    <p>Monitor your email processing and system performance</p>
                </div>
            </div>

            <!-- Welcome Message -->
            <div class="welcome-message">
                <h2>🎉 Welcome to Your Email Monitor Agent Dashboard!</h2>
                <p>Your email monitoring system is up and running. Here's what your agent can do:</p>
            </div>

            <!-- Stats Cards -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon emails">
                        <i class="fas fa-envelope"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalEmails">15</h3>
                        <p>Total Emails</p>
                        <span class="stat-change">+3 today</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon whatsapp">
                        <i class="fab fa-whatsapp"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalNotifications">12</h3>
                        <p>WhatsApp Sent</p>
                        <span class="stat-change">+3 today</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon sms">
                        <i class="fas fa-sms"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalSMS">10</h3>
                        <p>SMS Sent</p>
                        <span class="stat-change">+2 today</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon replies">
                        <i class="fas fa-reply"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalReplies">8</h3>
                        <p>Auto Replies</p>
                        <span class="stat-change">+2 today</span>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon employees">
                        <i class="fas fa-users"></i>
                    </div>
                    <div class="stat-content">
                        <h3 id="totalEmployees">3</h3>
                        <p>Employees</p>
                        <span class="stat-change">Active</span>
                    </div>
                </div>
            </div>

            <!-- Feature Overview -->
            <div class="feature-grid">
                <div class="feature-card">
                    <h3><i class="fas fa-envelope"></i> Email Processing</h3>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Monitors <EMAIL></li>
                        <li><i class="fas fa-check"></i> Filters <NAME_EMAIL></li>
                        <li><i class="fas fa-check"></i> AI-powered email analysis</li>
                        <li><i class="fas fa-check"></i> Automatic duplicate detection</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h3><i class="fab fa-whatsapp"></i> WhatsApp Notifications</h3>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Meta Cloud API integration</li>
                        <li><i class="fas fa-check"></i> Sends to +************</li>
                        <li><i class="fas fa-check"></i> Rich message formatting</li>
                        <li><i class="fas fa-check"></i> Delivery status tracking</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h3><i class="fas fa-robot"></i> AI Features</h3>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> OpenAI GPT-4 integration</li>
                        <li><i class="fas fa-check"></i> Email summarization</li>
                        <li><i class="fas fa-check"></i> WhatsApp message generation</li>
                        <li><i class="fas fa-check"></i> Auto-reply creation</li>
                    </ul>
                </div>

                <div class="feature-card">
                    <h3><i class="fas fa-cog"></i> System Status</h3>
                    <ul class="feature-list">
                        <li><i class="fas fa-check"></i> Email monitoring: Active</li>
                        <li><i class="fas fa-check"></i> Database: PostgreSQL connected</li>
                        <li><i class="fas fa-check"></i> WhatsApp API: Connected</li>
                        <li><i class="fas fa-check"></i> Auto-replies: Enabled</li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Email Logs Tab -->
        <div id="emails" class="tab-content">
            <div class="page-header">
                <div>
                    <h1><i class="fas fa-inbox"></i> Email Logs</h1>
                    <p>View and manage processed emails with AI summaries</p>
                </div>
                <div class="filters-section">
                    <select id="statusFilter" class="filter-select">
                        <option value="all">All Status</option>
                        <option value="processed">Processed</option>
                        <option value="pending">Pending</option>
                        <option value="failed">Failed</option>
                    </select>
                    <button class="btn-primary" onclick="loadEmails()">
                        <i class="fas fa-sync"></i> Refresh
                    </button>
                </div>
            </div>

            <div id="emailsContainer">
                <div class="loading-spinner" id="emailsLoading">
                    <i class="fas fa-spinner fa-spin"></i> Loading emails...
                </div>
                <div id="emailsList"></div>
            </div>
        </div>

        <!-- Employees Tab -->
        <div id="employees" class="tab-content">
            <div class="page-header">
                <div>
                    <h1><i class="fas fa-users"></i> Employee Management</h1>
                    <p>Manage WhatsApp recipients for email notifications</p>
                </div>
                <button class="btn-primary" onclick="openAddEmployeeModal()">
                    <i class="fas fa-plus"></i> Add Employee
                </button>
            </div>

            <div id="employeesContainer">
                <div class="loading-spinner" id="employeesLoading">
                    <i class="fas fa-spinner fa-spin"></i> Loading employees...
                </div>
                <div id="employeesList"></div>
            </div>
        </div>

        <div id="settings" class="tab-content">
            <div class="page-header">
                <div>
                    <h1><i class="fas fa-cog"></i> Settings</h1>
                    <p>Configure your Email Monitor Agent</p>
                </div>
            </div>
            <div class="welcome-message">
                <h2>⚙️ System Configuration</h2>
                <p>View and manage your email agent settings and API connections.</p>
                <p><strong>Status:</strong> All systems operational</p>
            </div>
        </div>
    </main>

    <!-- Add Employee Modal -->
    <div id="employeeModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-user-plus"></i> Add Employee</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <form id="employeeForm" onsubmit="submitEmployee(event)">
                <div class="form-group">
                    <label for="employeeName">
                        <i class="fas fa-user"></i> Employee Name
                    </label>
                    <input type="text" id="employeeName" name="name" required
                           placeholder="Enter employee name" class="form-input">
                </div>
                <div class="form-group">
                    <label for="employeePhone">
                        <i class="fab fa-whatsapp"></i> WhatsApp Number
                    </label>
                    <input type="tel" id="employeePhone" name="phone" required
                           placeholder="+************" class="form-input">
                    <small class="form-help">Include country code (e.g., +91 for India)</small>
                </div>
                <div class="form-actions">
                    <button type="button" class="btn-secondary" onclick="closeModal()">
                        Cancel
                    </button>
                    <button type="submit" class="btn-primary">
                        <i class="fas fa-save"></i> Save Employee
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Email Details Modal -->
    <div id="emailModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3><i class="fas fa-envelope"></i> Email Details</h3>
                <button class="modal-close" onclick="closeModal()">&times;</button>
            </div>
            <div id="emailDetails"></div>
        </div>
    </div>

    <!-- Toast Notifications -->
    <div id="toastContainer" class="toast-container"></div>

    <script>
        // Global variables
        let employees = [];
        let emails = [];

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeNavigation();
            loadEmployees();
            loadEmails();
            loadStats();

            // Simulate real-time updates
            setInterval(() => {
                const statusIndicator = document.getElementById('agentStatus');
                statusIndicator.innerHTML = '<i class="fas fa-circle"></i> <span>Agent Online</span>';
                loadStats(); // Refresh stats every 5 seconds
            }, 5000);

            console.log('📱 Email Monitor Dashboard loaded successfully!');
        });

        // Navigation functionality
        function initializeNavigation() {
            const navLinks = document.querySelectorAll('.nav-link');
            const tabContents = document.querySelectorAll('.tab-content');

            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active class from all nav links
                    navLinks.forEach(nl => nl.classList.remove('active'));

                    // Add active class to clicked link
                    this.classList.add('active');

                    // Hide all tab contents
                    tabContents.forEach(tc => tc.classList.remove('active'));

                    // Show selected tab content
                    const tabId = this.getAttribute('data-tab');
                    document.getElementById(tabId).classList.add('active');

                    // Load data when switching to specific tabs
                    if (tabId === 'employees') {
                        loadEmployees();
                    } else if (tabId === 'emails') {
                        loadEmails();
                    }
                });
            });
        }

        // API helper function
        async function apiCall(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                return response;
            } catch (error) {
                console.error('API call failed:', error);
                showToast('error', 'Network Error', 'Failed to connect to server');
                throw error;
            }
        }

        // Load employees from API
        async function loadEmployees() {
            const loadingElement = document.getElementById('employeesLoading');
            const listElement = document.getElementById('employeesList');

            try {
                loadingElement.style.display = 'flex';
                listElement.innerHTML = '';

                const response = await apiCall('/api/employees');

                if (response.ok) {
                    employees = await response.json();
                    renderEmployees(employees);
                } else {
                    throw new Error('Failed to load employees');
                }
            } catch (error) {
                console.error('Error loading employees:', error);
                listElement.innerHTML = `
                    <div class="employee-card">
                        <div class="employee-info">
                            <h4>Error loading employees</h4>
                            <div class="employee-phone">
                                <i class="fas fa-exclamation-triangle"></i>
                                Please check your connection and try again
                            </div>
                        </div>
                    </div>
                `;
            } finally {
                loadingElement.style.display = 'none';
            }
        }

        // Render employees list
        function renderEmployees(employeesList) {
            const listElement = document.getElementById('employeesList');

            if (!employeesList || employeesList.length === 0) {
                listElement.innerHTML = `
                    <div class="employee-card">
                        <div class="employee-avatar">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="employee-info">
                            <h4>No employees found</h4>
                            <div class="employee-phone">
                                <i class="fas fa-info-circle"></i>
                                Click "Add Employee" to add team members
                            </div>
                        </div>
                    </div>
                `;
                return;
            }

            const employeesHtml = employeesList.map(employee => {
                const initials = getEmployeeInitials(employee.name);

                return `
                    <div class="employee-card">
                        <div class="employee-avatar">${initials}</div>
                        <div class="employee-info">
                            <h4>${employee.name}</h4>
                            <div class="employee-phone">
                                <i class="fab fa-whatsapp"></i>
                                ${employee.phone}
                            </div>
                        </div>
                        <div class="employee-actions">
                            <button class="btn-icon edit" onclick="editEmployee(${employee.id})" title="Edit Employee">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn-icon delete" onclick="deleteEmployee(${employee.id})" title="Delete Employee">
                                <i class="fas fa-trash"></i>
                            </button>
                        </div>
                    </div>
                `;
            }).join('');

            listElement.innerHTML = employeesHtml;
        }

        // Get employee initials
        function getEmployeeInitials(name) {
            if (!name) return '?';
            const words = name.trim().split(' ');
            if (words.length === 1) {
                return words[0].charAt(0).toUpperCase();
            }
            return (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
        }

        // Load emails from API
        async function loadEmails() {
            const loadingElement = document.getElementById('emailsLoading');
            const listElement = document.getElementById('emailsList');

            try {
                loadingElement.style.display = 'flex';
                listElement.innerHTML = '';

                const statusFilter = document.getElementById('statusFilter')?.value || 'all';
                let url = '/api/emails';

                if (statusFilter !== 'all') {
                    url += `?status=${statusFilter}`;
                }

                const response = await apiCall(url);

                if (response.ok) {
                    emails = await response.json();
                    renderEmails(emails);
                } else {
                    throw new Error('Failed to load emails');
                }
            } catch (error) {
                console.error('Error loading emails:', error);
                listElement.innerHTML = `
                    <div class="email-card">
                        <div style="text-align: center; padding: 2rem;">
                            <h4>Error loading emails</h4>
                            <p>Please check your connection and try again</p>
                            <button class="btn-primary" onclick="loadEmails()">
                                <i class="fas fa-sync"></i> Retry
                            </button>
                        </div>
                    </div>
                `;
            } finally {
                loadingElement.style.display = 'none';
            }
        }

        // Render emails list
        function renderEmails(emailsList) {
            const listElement = document.getElementById('emailsList');

            if (!emailsList || emailsList.length === 0) {
                listElement.innerHTML = `
                    <div class="email-card">
                        <div style="text-align: center; padding: 2rem;">
                            <h4>No emails found</h4>
                            <p>No emails match your current filters or no emails have been processed yet.</p>
                        </div>
                    </div>
                `;
                return;
            }

            const emailsHtml = emailsList.map(email => {
                const statusClass = getEmailStatusClass(email.status);
                const hasWhatsApp = email.whatsapp_notifications && email.whatsapp_notifications.length > 0;
                const hasSMS = email.sms_notifications && email.sms_notifications.length > 0;
                const hasReply = email.email_replies && email.email_replies.length > 0;

                return `
                    <div class="email-card" onclick="showEmailDetails(${email.id})">
                        <div class="email-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
                            <div>
                                <h4 style="margin: 0; color: var(--text-primary);">${email.subject || 'No Subject'}</h4>
                                <div style="color: var(--text-secondary); font-size: 0.875rem; margin-top: 0.25rem;">
                                    From: ${extractEmailFromSender(email.sender)}
                                </div>
                            </div>
                            <div style="text-align: right;">
                                <span class="status-badge ${statusClass}">${email.status}</span>
                                <div style="color: var(--text-muted); font-size: 0.75rem; margin-top: 0.25rem;">
                                    ${formatDate(email.received_at)}
                                </div>
                            </div>
                        </div>

                        ${email.summary ? `
                            <div style="margin-bottom: 1rem;">
                                <strong>AI Summary:</strong>
                                <p style="margin: 0.5rem 0; color: var(--text-secondary); line-height: 1.5;">
                                    ${truncateText(email.summary, 150)}
                                </p>
                            </div>
                        ` : ''}

                        <div style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
                            ${hasWhatsApp ? '<span class="email-badge whatsapp"><i class="fab fa-whatsapp"></i> WhatsApp Sent</span>' : ''}
                            ${hasSMS ? '<span class="email-badge sms"><i class="fas fa-sms"></i> SMS Sent</span>' : ''}
                            ${hasReply ? '<span class="email-badge reply"><i class="fas fa-reply"></i> Auto-Reply Sent</span>' : ''}
                        </div>
                    </div>
                `;
            }).join('');

            listElement.innerHTML = emailsHtml;
        }

        // Load statistics from API
        async function loadStats() {
            try {
                const response = await apiCall('/api/stats');

                if (response.ok) {
                    const stats = await response.json();
                    updateStatsDisplay(stats);
                } else {
                    console.error('Failed to load stats');
                }
            } catch (error) {
                console.error('Error loading stats:', error);
            }
        }

        // Update statistics display
        function updateStatsDisplay(stats) {
            // Update total emails
            const totalEmailsElement = document.getElementById('totalEmails');
            if (totalEmailsElement) {
                totalEmailsElement.textContent = stats.emails.total || 0;
            }

            // Update WhatsApp notifications
            const totalNotificationsElement = document.getElementById('totalNotifications');
            if (totalNotificationsElement) {
                totalNotificationsElement.textContent = stats.notifications.total || 0;
            }

            // Update SMS notifications
            const totalSMSElement = document.getElementById('totalSMS');
            if (totalSMSElement) {
                totalSMSElement.textContent = stats.sms_notifications.total || 0;
            }

            // Update email replies
            const totalRepliesElement = document.getElementById('totalReplies');
            if (totalRepliesElement) {
                totalRepliesElement.textContent = stats.replies.total || 0;
            }

            // Update employees count
            const totalEmployeesElement = document.getElementById('totalEmployees');
            if (totalEmployeesElement) {
                totalEmployeesElement.textContent = employees.length || 0;
            }
        }

        // Helper functions
        function getEmailStatusClass(status) {
            const statusMap = {
                'processed': 'processed',
                'pending': 'pending',
                'failed': 'failed',
                'received': 'pending'
            };
            return statusMap[status] || 'pending';
        }

        function extractEmailFromSender(sender) {
            if (!sender) return 'Unknown';
            const match = sender.match(/<(.+)>/);
            return match ? match[1] : sender;
        }

        function formatDate(dateString) {
            if (!dateString) return 'Unknown';
            const date = new Date(dateString);
            return date.toLocaleDateString() + ' ' + date.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
        }

        function truncateText(text, maxLength) {
            if (!text) return '';
            if (text.length <= maxLength) return text;
            return text.substring(0, maxLength) + '...';
        }

        // Modal management
        function openAddEmployeeModal() {
            const modal = document.getElementById('employeeModal');
            const form = document.getElementById('employeeForm');

            // Reset form
            form.reset();

            // Show modal
            modal.classList.add('active');

            // Focus on name field
            setTimeout(() => {
                document.getElementById('employeeName').focus();
            }, 100);
        }

        function closeModal() {
            const modals = document.querySelectorAll('.modal');
            modals.forEach(modal => modal.classList.remove('active'));
        }

        // Employee CRUD operations
        async function submitEmployee(event) {
            event.preventDefault();

            const name = document.getElementById('employeeName').value.trim();
            const phone = document.getElementById('employeePhone').value.trim();

            if (!name || !phone) {
                showToast('warning', 'Validation Error', 'Please fill in all fields');
                return;
            }

            // Basic phone validation
            const phoneRegex = /^\+?[\d\s\-\(\)]+$/;
            if (!phoneRegex.test(phone)) {
                showToast('warning', 'Validation Error', 'Please enter a valid phone number');
                return;
            }

            try {
                const response = await apiCall('/api/employees', {
                    method: 'POST',
                    body: JSON.stringify({ name, phone })
                });

                if (response.ok) {
                    const newEmployee = await response.json();
                    showToast('success', 'Success', 'Employee added successfully');
                    closeModal();
                    loadEmployees(); // Reload the list
                } else {
                    const error = await response.json();
                    showToast('error', 'Error', error.detail || 'Failed to add employee');
                }
            } catch (error) {
                console.error('Error adding employee:', error);
                showToast('error', 'Error', 'Failed to add employee');
            }
        }

        async function editEmployee(employeeId) {
            const employee = employees.find(emp => emp.id === employeeId);
            if (!employee) {
                showToast('error', 'Error', 'Employee not found');
                return;
            }

            // Populate form with existing data
            document.getElementById('employeeName').value = employee.name;
            document.getElementById('employeePhone').value = employee.phone;

            // Change form submission to update
            const form = document.getElementById('employeeForm');
            form.onsubmit = async (event) => {
                event.preventDefault();
                await updateEmployee(employeeId);
            };

            // Change modal title
            document.querySelector('#employeeModal .modal-header h3').innerHTML =
                '<i class="fas fa-user-edit"></i> Edit Employee';

            // Show modal
            document.getElementById('employeeModal').classList.add('active');
        }

        async function updateEmployee(employeeId) {
            const name = document.getElementById('employeeName').value.trim();
            const phone = document.getElementById('employeePhone').value.trim();

            if (!name || !phone) {
                showToast('warning', 'Validation Error', 'Please fill in all fields');
                return;
            }

            try {
                const response = await apiCall(`/api/employees/${employeeId}`, {
                    method: 'PUT',
                    body: JSON.stringify({ name, phone })
                });

                if (response.ok) {
                    showToast('success', 'Success', 'Employee updated successfully');
                    closeModal();

                    // Reset form submission
                    const form = document.getElementById('employeeForm');
                    form.onsubmit = submitEmployee;

                    // Reset modal title
                    document.querySelector('#employeeModal .modal-header h3').innerHTML =
                        '<i class="fas fa-user-plus"></i> Add Employee';

                    loadEmployees(); // Reload the list
                } else {
                    const error = await response.json();
                    showToast('error', 'Error', error.detail || 'Failed to update employee');
                }
            } catch (error) {
                console.error('Error updating employee:', error);
                showToast('error', 'Error', 'Failed to update employee');
            }
        }

        async function deleteEmployee(employeeId) {
            const employee = employees.find(emp => emp.id === employeeId);
            const employeeName = employee ? employee.name : 'this employee';

            if (!confirm(`Are you sure you want to delete ${employeeName}? This action cannot be undone.`)) {
                return;
            }

            try {
                const response = await apiCall(`/api/employees/${employeeId}`, {
                    method: 'DELETE'
                });

                if (response.ok) {
                    showToast('success', 'Success', 'Employee deleted successfully');
                    loadEmployees(); // Reload the list
                } else {
                    const error = await response.json();
                    showToast('error', 'Error', error.detail || 'Failed to delete employee');
                }
            } catch (error) {
                console.error('Error deleting employee:', error);
                showToast('error', 'Error', 'Failed to delete employee');
            }
        }

        // Email details modal
        async function showEmailDetails(emailId) {
            try {
                const response = await apiCall(`/api/emails/${emailId}`);

                if (response.ok) {
                    const email = await response.json();
                    renderEmailDetails(email);
                    document.getElementById('emailModal').classList.add('active');
                } else {
                    showToast('error', 'Error', 'Failed to load email details');
                }
            } catch (error) {
                console.error('Error loading email details:', error);
                showToast('error', 'Error', 'Failed to load email details');
            }
        }

        function renderEmailDetails(email) {
            const container = document.getElementById('emailDetails');

            container.innerHTML = `
                <div style="padding: 1.5rem;">
                    <div style="margin-bottom: 1.5rem;">
                        <h4 style="margin: 0 0 1rem 0;">${email.subject || 'No Subject'}</h4>
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; font-size: 0.875rem;">
                            <div><strong>From:</strong> ${email.sender}</div>
                            <div><strong>To:</strong> ${email.recipient}</div>
                            <div><strong>Received:</strong> ${formatDate(email.received_at)}</div>
                            <div><strong>Status:</strong> <span class="status-badge ${getEmailStatusClass(email.status)}">${email.status}</span></div>
                        </div>
                    </div>

                    ${email.summary ? `
                        <div style="margin-bottom: 1.5rem;">
                            <h5 style="margin: 0 0 0.5rem 0;"><i class="fas fa-robot"></i> AI Summary</h5>
                            <div style="background: var(--bg-tertiary); padding: 1rem; border-radius: var(--radius-md); line-height: 1.6;">
                                ${email.summary.replace(/\n/g, '<br>')}
                            </div>
                        </div>
                    ` : ''}

                    ${email.whatsapp_summary ? `
                        <div style="margin-bottom: 1.5rem;">
                            <h5 style="margin: 0 0 0.5rem 0;"><i class="fab fa-whatsapp"></i> WhatsApp Message</h5>
                            <div style="background: rgba(37, 211, 102, 0.1); padding: 1rem; border-radius: var(--radius-md); line-height: 1.6;">
                                ${email.whatsapp_summary}
                            </div>
                        </div>
                    ` : ''}

                    ${email.auto_reply_text ? `
                        <div style="margin-bottom: 1.5rem;">
                            <h5 style="margin: 0 0 0.5rem 0;"><i class="fas fa-reply"></i> Auto-Reply</h5>
                            <div style="background: rgba(59, 130, 246, 0.1); padding: 1rem; border-radius: var(--radius-md); line-height: 1.6;">
                                ${email.auto_reply_text}
                            </div>
                        </div>
                    ` : ''}
                </div>
            `;
        }

        // Toast notifications
        function showToast(type, title, message) {
            const container = document.getElementById('toastContainer');
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;

            toast.innerHTML = `
                <div style="font-weight: 600; margin-bottom: 0.25rem;">${title}</div>
                <div style="font-size: 0.875rem; color: var(--text-secondary);">${message}</div>
            `;

            container.appendChild(toast);

            // Auto remove after 5 seconds
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 5000);
        }

        // Add CSS for status badges and email badges
        const additionalStyles = `
            <style>
                .status-badge {
                    padding: 0.25rem 0.5rem;
                    border-radius: 0.375rem;
                    font-size: 0.75rem;
                    font-weight: 500;
                    text-transform: uppercase;
                }

                .status-badge.processed {
                    background-color: rgba(16, 185, 129, 0.1);
                    color: var(--success-color);
                }

                .status-badge.pending {
                    background-color: rgba(245, 158, 11, 0.1);
                    color: var(--warning-color);
                }

                .status-badge.failed {
                    background-color: rgba(239, 68, 68, 0.1);
                    color: var(--danger-color);
                }

                .email-badge {
                    display: inline-flex;
                    align-items: center;
                    gap: 0.25rem;
                    padding: 0.25rem 0.5rem;
                    border-radius: 0.375rem;
                    font-size: 0.75rem;
                    font-weight: 500;
                }

                .email-badge.whatsapp {
                    background-color: rgba(37, 211, 102, 0.1);
                    color: var(--whatsapp-color);
                }

                .email-badge.sms {
                    background-color: rgba(139, 92, 246, 0.1);
                    color: #8b5cf6;
                }

                .email-badge.reply {
                    background-color: rgba(59, 130, 246, 0.1);
                    color: var(--primary-color);
                }
            </style>
        `;

        document.head.insertAdjacentHTML('beforeend', additionalStyles);
    </script>
</body>
</html>
