#!/usr/bin/env python3
"""
Test Employee CRUD operations to ensure all changes are properly synchronized
across the dashboard, API, and notification system.
"""

import sys
import os
import time
import requests
import json
sys.path.append('.')

from app.services.employee_service import get_employee_service

def test_employee_service_directly():
    """Test the shared employee service directly"""
    print("🔧 Testing Employee Service Directly")
    print("=" * 50)
    
    employee_service = get_employee_service()
    
    # Test READ - Get all employees
    print("📖 READ: Getting all employees...")
    employees = employee_service.get_all_employees()
    print(f"   Found {len(employees)} employees:")
    for emp in employees:
        print(f"   - {emp['name']} ({emp['phone']}) - ID: {emp['id']}")
    
    # Test UPDATE - Update Karth<PERSON>'s phone number
    print(f"\n✏️ UPDATE: Updating Karth<PERSON>'s phone number...")
    karthik_id = 3
    old_karthik = employee_service.get_employee(karthik_id)
    print(f"   Before: {old_karthik['name']} - {old_karthik['phone']}")
    
    updated_karthik = employee_service.update_employee(karthik_id, phone="+919999999999")
    print(f"   After: {updated_karthik['name']} - {updated_karthik['phone']}")
    
    # Test CREATE - Add new employee
    print(f"\n➕ CREATE: Adding new employee...")
    new_employee = employee_service.create_employee("Test Employee", "+919876543210")
    print(f"   Created: {new_employee['name']} ({new_employee['phone']}) - ID: {new_employee['id']}")
    
    # Test READ again to verify changes
    print(f"\n📖 READ: Verifying changes...")
    employees = employee_service.get_all_employees()
    print(f"   Total employees: {len(employees)}")
    for emp in employees:
        print(f"   - {emp['name']} ({emp['phone']}) - ID: {emp['id']}")
    
    # Test DELETE - Remove test employee
    print(f"\n🗑️ DELETE: Removing test employee...")
    deleted = employee_service.delete_employee(new_employee['id'])
    print(f"   Deleted: {deleted}")
    
    # Final verification
    print(f"\n📖 FINAL READ: Final state...")
    employees = employee_service.get_all_employees()
    print(f"   Total employees: {len(employees)}")
    for emp in employees:
        print(f"   - {emp['name']} ({emp['phone']}) - ID: {emp['id']}")
    
    return employees

def test_api_endpoints():
    """Test the API endpoints to ensure they use the shared service"""
    print("\n🌐 Testing API Endpoints")
    print("=" * 50)
    
    base_url = "http://localhost:8000"
    
    try:
        # Test GET /api/employees
        print("📖 GET /api/employees...")
        response = requests.get(f"{base_url}/api/employees", timeout=10)
        if response.status_code == 200:
            employees = response.json()
            print(f"   ✅ API returned {len(employees)} employees")
            for emp in employees:
                print(f"   - {emp['name']} ({emp['phone']}) - ID: {emp['id']}")
        else:
            print(f"   ❌ API error: {response.status_code}")
            return False
        
        # Test PUT /api/employees/{id} - Update Karthik's phone back
        print(f"\n✏️ PUT /api/employees/3 - Updating Karthik's phone...")
        update_data = {"phone": "+919788002618"}  # Original number
        response = requests.put(
            f"{base_url}/api/employees/3", 
            json=update_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        if response.status_code == 200:
            updated_emp = response.json()
            print(f"   ✅ Updated: {updated_emp['name']} - {updated_emp['phone']}")
        else:
            print(f"   ❌ Update failed: {response.status_code}")
            return False
        
        # Test POST /api/employees - Create new employee
        print(f"\n➕ POST /api/employees - Creating new employee...")
        new_emp_data = {"name": "API Test Employee", "phone": "+919111111111"}
        response = requests.post(
            f"{base_url}/api/employees",
            json=new_emp_data,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        if response.status_code == 200:
            new_emp = response.json()
            print(f"   ✅ Created: {new_emp['name']} ({new_emp['phone']}) - ID: {new_emp['id']}")
            test_emp_id = new_emp['id']
        else:
            print(f"   ❌ Create failed: {response.status_code}")
            return False
        
        # Test DELETE /api/employees/{id}
        print(f"\n🗑️ DELETE /api/employees/{test_emp_id} - Deleting test employee...")
        response = requests.delete(f"{base_url}/api/employees/{test_emp_id}", timeout=10)
        if response.status_code == 200:
            print(f"   ✅ Deleted successfully")
        else:
            print(f"   ❌ Delete failed: {response.status_code}")
            return False
        
        # Final verification
        print(f"\n📖 Final GET /api/employees...")
        response = requests.get(f"{base_url}/api/employees", timeout=10)
        if response.status_code == 200:
            employees = response.json()
            print(f"   ✅ Final state: {len(employees)} employees")
            for emp in employees:
                print(f"   - {emp['name']} ({emp['phone']}) - ID: {emp['id']}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"   ❌ API connection error: {e}")
        print(f"   💡 Make sure the main API server is running on {base_url}")
        return False

def test_notification_system():
    """Test that the notification system uses the updated employee data"""
    print("\n📱 Testing Notification System Integration")
    print("=" * 50)
    
    try:
        from app.services.notification_service import get_notification_service
        from app.worker import get_config
        
        config = get_config()
        notification_service = get_notification_service(config)
        
        # Test getting active employees from notification service
        print("📖 Getting employees from notification service...")
        active_employees = notification_service._get_active_employees(None)
        
        print(f"   ✅ Notification service found {len(active_employees)} active employees:")
        for emp in active_employees:
            print(f"   - {emp['name']} ({emp['phone']}) - ID: {emp['id']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Notification system test error: {e}")
        return False

def check_data_persistence():
    """Check if data persists in the JSON file"""
    print("\n💾 Testing Data Persistence")
    print("=" * 50)
    
    try:
        # Check if JSON file exists
        json_file = "employees_data.json"
        if os.path.exists(json_file):
            with open(json_file, 'r') as f:
                data = json.load(f)
            
            employees = data.get('employees', [])
            next_id = data.get('next_id', 1)
            
            print(f"   ✅ JSON file exists with {len(employees)} employees")
            print(f"   📄 File: {json_file}")
            print(f"   🆔 Next ID: {next_id}")
            
            for emp in employees:
                print(f"   - {emp['name']} ({emp['phone']}) - ID: {emp['id']}")
            
            return True
        else:
            print(f"   ⚠️ JSON file not found: {json_file}")
            return False
            
    except Exception as e:
        print(f"   ❌ Data persistence test error: {e}")
        return False

def main():
    """Main test function"""
    print("🚀 Employee CRUD Operations Test")
    print("=" * 60)
    print("This test verifies that all CRUD operations work correctly")
    print("and are synchronized across the dashboard, API, and notifications.")
    print("=" * 60)
    
    # Test 1: Direct employee service
    employees = test_employee_service_directly()
    
    # Test 2: API endpoints
    api_success = test_api_endpoints()
    
    # Test 3: Notification system integration
    notification_success = test_notification_system()
    
    # Test 4: Data persistence
    persistence_success = check_data_persistence()
    
    # Summary
    print(f"\n🎯 Test Summary")
    print("=" * 50)
    print(f"   🔧 Employee Service: ✅")
    print(f"   🌐 API Endpoints: {'✅' if api_success else '❌'}")
    print(f"   📱 Notification System: {'✅' if notification_success else '❌'}")
    print(f"   💾 Data Persistence: {'✅' if persistence_success else '❌'}")
    
    all_success = api_success and notification_success and persistence_success
    
    if all_success:
        print(f"\n🎉 ALL TESTS PASSED!")
        print(f"   ✅ Dashboard updates will be reflected everywhere")
        print(f"   ✅ CRUD operations work correctly")
        print(f"   ✅ Data persists between server restarts")
        print(f"   ✅ Notifications use updated employee data")
    else:
        print(f"\n⚠️ SOME TESTS FAILED!")
        print(f"   💡 Check the error messages above")
        print(f"   💡 Make sure the main API server is running")
    
    print(f"\n💡 Next Steps:")
    print(f"   • Try updating Karthik's phone number in the dashboard")
    print(f"   • Send a test email to trigger notifications")
    print(f"   • Verify that SMS/WhatsApp go to the updated number")

if __name__ == "__main__":
    main()
