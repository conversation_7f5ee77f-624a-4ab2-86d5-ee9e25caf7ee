"""
Vonage SMS API integration for sending SMS notifications to management employees.
"""

import os
import requests
import logging
from typing import Dict, List, Optional
from datetime import datetime
from sqlalchemy.orm import Session

from ..models import EmailLog, SMSNotification

# Set up logging
logger = logging.getLogger(__name__)


class VonageSMS:
    """
    Vonage SMS API integration for sending SMS notifications to employees.
    """
    
    def __init__(self, 
                 api_key: str,
                 api_secret: str,
                 from_number: str = "EmailAgent"):
        """
        Initialize the Vonage SMS service.
        
        Args:
            api_key: Vonage API key
            api_secret: Vonage API secret
            from_number: SMS sender ID or phone number
        """
        self.api_key = api_key
        self.api_secret = api_secret
        self.from_number = from_number
        self.base_url = "https://rest.nexmo.com/sms/json"
        
    def send_sms(self, 
                 recipient_phone: str, 
                 message: str,
                 email_log: EmailLog,
                 employee_name: str = None) -> Dict:
        """
        Send SMS notification to a recipient.
        
        Args:
            recipient_phone: Phone number to send SMS to
            message: SMS message content
            email_log: EmailLog instance for context
            employee_name: Employee name for personalization
            
        Returns:
            Dict: SMS response with message_id and status
        """
        try:
            # Format the SMS message
            formatted_message = self._format_sms_message(email_log, employee_name)
            
            # Vonage SMS API payload
            payload = {
                "from": self.from_number,
                "to": recipient_phone,
                "text": formatted_message,
                "api_key": self.api_key,
                "api_secret": self.api_secret
            }
            
            logger.info(f"Sending SMS to {recipient_phone} for employee {employee_name}")
            
            response = requests.post(
                self.base_url,
                data=payload,
                timeout=30
            )
            
            if response.status_code == 200:
                sms_data = response.json()
                messages = sms_data.get('messages', [])
                
                if messages and messages[0].get('status') == '0':
                    # Success
                    message_data = messages[0]
                    logger.info(f"SMS sent successfully: {message_data.get('message-id')}")
                    return {
                        'success': True,
                        'message_id': message_data.get('message-id'),
                        'status': 'sent',
                        'to': message_data.get('to'),
                        'remaining_balance': message_data.get('remaining-balance'),
                        'message_price': message_data.get('message-price'),
                        'network': message_data.get('network')
                    }
                else:
                    # Error in message
                    error_msg = messages[0].get('error-text', 'Unknown SMS error') if messages else 'No message data'
                    logger.error(f"SMS error: {error_msg}")
                    return {
                        'success': False,
                        'error': error_msg,
                        'status': 'failed'
                    }
            else:
                error_msg = f"Vonage SMS API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg,
                    'status_code': response.status_code
                }
                
        except requests.exceptions.Timeout:
            error_msg = "Vonage SMS API call timed out"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}
        except Exception as e:
            error_msg = f"Unexpected error sending SMS: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}

    def send_notifications(self, email_log: EmailLog, db_session: Session) -> List[SMSNotification]:
        """
        Send SMS notifications to all active management employees.
        
        Args:
            email_log: EmailLog instance
            db_session: Database session
            
        Returns:
            List[SMSNotification]: List of SMS notification records
        """
        from ..worker import employees_db  # Import here to avoid circular imports
        
        notifications = []
        
        # Get active employees
        active_employees = [emp for emp in employees_db if emp.get("status") == "active"]
        
        if not active_employees:
            logger.warning("No active employees found for SMS notifications")
            return notifications
            
        logger.info(f"Sending SMS notifications to {len(active_employees)} active employees")
        
        for employee in active_employees:
            try:
                # Send SMS
                result = self.send_sms(
                    recipient_phone=employee['phone'],
                    message="",  # Will be formatted in send_sms method
                    email_log=email_log,
                    employee_name=employee['name']
                )
                
                # Create SMS notification record
                notification = SMSNotification(
                    email_log_id=email_log.id,
                    recipient=employee['phone'],
                    employee_name=employee['name'],
                    message=self._format_sms_message(email_log, employee['name']),
                    status='sent' if result['success'] else 'failed',
                    error_message=result.get('error'),
                    message_id=result.get('message_id'),
                    sent_at=datetime.utcnow() if result['success'] else None
                )
                
                db_session.add(notification)
                notifications.append(notification)
                
                if result['success']:
                    logger.info(f"SMS sent successfully to {employee['name']} ({employee['phone']})")
                else:
                    logger.error(f"Failed to send SMS to {employee['name']}: {result.get('error')}")
                    
            except Exception as e:
                logger.error(f"Error sending SMS to {employee['name']}: {str(e)}")
                
                # Create failed notification record
                notification = SMSNotification(
                    email_log_id=email_log.id,
                    recipient=employee['phone'],
                    employee_name=employee['name'],
                    message=self._format_sms_message(email_log, employee['name']),
                    status='failed',
                    error_message=str(e)
                )
                
                db_session.add(notification)
                notifications.append(notification)
        
        try:
            db_session.commit()
            logger.info(f"Saved {len(notifications)} SMS notification records")
        except Exception as e:
            logger.error(f"Error saving SMS notifications: {str(e)}")
            db_session.rollback()
            
        return notifications

    def _format_sms_message(self, email_log: EmailLog, employee_name: str = None) -> str:
        """
        Format the SMS message content.
        
        Args:
            email_log: EmailLog instance
            employee_name: Employee name for personalization
            
        Returns:
            str: Formatted SMS message
        """
        # Extract sender name (remove email if present)
        sender_name = email_log.sender.split('<')[0].strip() if '<' in email_log.sender else email_log.sender
        
        # Create personalized message
        greeting = f"Hi {employee_name.split()[0]}, " if employee_name else "Hi, "
        
        message = f"{greeting}New email from {sender_name}"
        
        if email_log.subject:
            # Limit subject length for SMS
            subject = email_log.subject[:50] + "..." if len(email_log.subject) > 50 else email_log.subject
            message += f"\nSubject: {subject}"
            
        if email_log.whatsapp_summary:
            # Limit summary length for SMS (SMS has 160 char limit per message)
            remaining_chars = 160 - len(message) - 20  # Leave space for footer
            if remaining_chars > 20:
                summary = email_log.whatsapp_summary[:remaining_chars] + "..." if len(email_log.whatsapp_summary) > remaining_chars else email_log.whatsapp_summary
                message += f"\n{summary}"
                
        message += "\n- Email Monitor Agent"
        
        return message


class MockSMSService:
    """
    Mock SMS service for testing and development.
    """
    
    def __init__(self, *args, **kwargs):
        logger.info("Using Mock SMS Service")
        
    def send_sms(self, recipient_phone: str, message: str, email_log: EmailLog, employee_name: str = None) -> Dict:
        """Mock SMS sending"""
        logger.info(f"MOCK SMS to {recipient_phone} ({employee_name}): {message[:50]}...")
        return {
            'success': True,
            'message_id': f'mock_sms_{datetime.utcnow().timestamp()}',
            'status': 'sent'
        }
        
    def send_notifications(self, email_log: EmailLog, db_session: Session) -> List:
        """Mock SMS notifications"""
        logger.info(f"MOCK SMS notifications for email {email_log.id}")
        return []


def get_sms_service(config: Dict) -> VonageSMS:
    """
    Factory function to get the SMS service based on configuration.
    
    Args:
        config: Configuration dictionary containing:
            - sms_enabled: Whether SMS is enabled
            - vonage_sms_api_key: Vonage SMS API key
            - vonage_sms_api_secret: Vonage SMS API secret
            - vonage_sms_from_number: SMS sender ID
        
    Returns:
        VonageSMS: SMS service instance
    """
    if not config.get('sms_enabled', False):
        return MockSMSService()
        
    return VonageSMS(
        api_key=config['vonage_sms_api_key'],
        api_secret=config['vonage_sms_api_secret'],
        from_number=config.get('vonage_sms_from_number', 'EmailAgent')
    )
