#!/usr/bin/env python3
"""
Test script to verify SMS integration with Vonage
"""

import sys
import os
sys.path.append('.')

from app.models import EmailLog, SMSNotification, get_db
from app.services.vonage_sms import get_sms_service
from app.worker import employees_db, get_config
from datetime import datetime

def test_sms_integration():
    """Test SMS integration with Vonage"""
    
    print("🧪 Testing SMS Integration with Vonage")
    print("=" * 60)
    
    # Get configuration
    config = get_config()
    print(f"\n📋 SMS Configuration:")
    print(f"  • SMS Enabled: {config.get('sms_enabled', False)}")
    print(f"  • SMS Provider: {config.get('sms_provider', 'N/A')}")
    print(f"  • Vonage SMS API Key: {config.get('vonage_sms_api_key', 'N/A')[:8]}...")
    print(f"  • SMS From Number: {config.get('vonage_sms_from_number', 'N/A')}")
    
    # Show current active employees
    print("\n📋 Current Active Employees:")
    active_employees = [emp for emp in employees_db if emp.get("status") == "active"]
    for emp in active_employees:
        print(f"  • {emp['name']} - {emp['phone']}")
    
    if not active_employees:
        print("  ❌ No active employees found!")
        return
    
    # Create a test email log
    print("\n📧 Creating Test Email Log...")
    db = get_db()
    
    test_email = EmailLog(
        message_id="test-sms-integration-001",
        sender="<EMAIL>",
        recipient="<EMAIL>",
        subject="Test SMS Integration",
        whatsapp_summary="This is a test email for SMS integration with Vonage. The system is working correctly.",
        status="processed"
    )
    
    db.add(test_email)
    db.commit()
    print(f"  ✅ Created test email with ID: {test_email.id}")
    
    # Test SMS service
    print("\n📱 Testing SMS Service...")
    sms_service = get_sms_service(config)
    
    if hasattr(sms_service, 'send_notifications'):
        # Test sending SMS notifications to all employees
        print("  📤 Sending SMS notifications to all active employees...")
        sms_notifications = sms_service.send_notifications(test_email, db)
        
        print(f"\n📊 SMS Notification Results:")
        print(f"  • Total SMS sent: {len(sms_notifications)}")
        
        for notification in sms_notifications:
            status_icon = "✅" if notification.status == "sent" else "❌"
            print(f"  {status_icon} {notification.employee_name} ({notification.recipient}): {notification.status}")
            if notification.error_message:
                print(f"    Error: {notification.error_message}")
            if notification.message_id:
                print(f"    Message ID: {notification.message_id}")
    else:
        print("  ⚠️  SMS service is in mock mode")
    
    # Test individual SMS sending
    print("\n📱 Testing Individual SMS Sending...")
    if active_employees:
        test_employee = active_employees[0]
        result = sms_service.send_sms(
            recipient_phone=test_employee['phone'],
            message="Test SMS message",
            email_log=test_email,
            employee_name=test_employee['name']
        )
        
        status_icon = "✅" if result.get('success') else "❌"
        print(f"  {status_icon} Individual SMS to {test_employee['name']}: {result.get('status', 'unknown')}")
        if result.get('error'):
            print(f"    Error: {result['error']}")
        if result.get('message_id'):
            print(f"    Message ID: {result['message_id']}")
    
    # Check database records
    print("\n💾 Checking Database Records...")
    sms_count = db.query(SMSNotification).filter(SMSNotification.email_log_id == test_email.id).count()
    print(f"  • SMS notifications in database: {sms_count}")
    
    if sms_count > 0:
        recent_sms = db.query(SMSNotification).filter(
            SMSNotification.email_log_id == test_email.id
        ).order_by(SMSNotification.id.desc()).first()
        
        print(f"  • Most recent SMS:")
        print(f"    - Recipient: {recent_sms.recipient}")
        print(f"    - Employee: {recent_sms.employee_name}")
        print(f"    - Status: {recent_sms.status}")
        print(f"    - Message: {recent_sms.message[:50]}...")
        if recent_sms.sent_at:
            print(f"    - Sent at: {recent_sms.sent_at}")
    
    # Cleanup
    print("\n🧹 Cleaning up test data...")
    db.query(SMSNotification).filter(SMSNotification.email_log_id == test_email.id).delete()
    db.query(EmailLog).filter(EmailLog.id == test_email.id).delete()
    db.commit()
    print("  ✅ Test data cleaned up")
    
    print("\n🎉 SMS Integration Test Complete!")
    print("=" * 60)

def test_sms_configuration():
    """Test SMS configuration and environment variables"""
    
    print("\n🔧 Testing SMS Configuration...")
    config = get_config()
    
    required_vars = [
        'sms_enabled',
        'vonage_sms_api_key',
        'vonage_sms_api_secret',
        'vonage_sms_from_number'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not config.get(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"  ❌ Missing configuration variables: {', '.join(missing_vars)}")
        return False
    else:
        print("  ✅ All required SMS configuration variables are set")
        return True

if __name__ == "__main__":
    try:
        # Test configuration first
        config_ok = test_sms_configuration()
        
        if config_ok:
            # Run integration test
            test_sms_integration()
        else:
            print("\n❌ SMS configuration is incomplete. Please check your .env file.")
            
    except Exception as e:
        print(f"\n❌ Error during SMS integration test: {str(e)}")
        import traceback
        traceback.print_exc()
