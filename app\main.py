from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, Query
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel
import logging
import os

from .models import get_db, EmailLog, WhatsAppNotification, SMSNotification, EmailReply
from .schemas import (
    EmailLogResponse, EmailLogDetail, NotificationResponse, SMSNotificationResponse,
    ReplyResponse, StatsResponse, StatusCount
)

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="Email Monitor Agent API",
    description="AI-powered email monitoring and notification system",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

@app.get("/")
def read_root():
    """Root endpoint"""
    return {"message": "Email Monitor Agent API", "version": "1.0.0"}

@app.get("/api/emails", response_model=List[EmailLogResponse])
def get_emails(
    status: Optional[str] = Query(None, description="Filter by status"),
    sender: Optional[str] = Query(None, description="Filter by sender"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_db)
):
    """Get all processed emails with optional filtering"""
    try:
        query = db.query(EmailLog)

        # Apply filters
        if status:
            query = query.filter(EmailLog.status == status)
        if sender:
            query = query.filter(EmailLog.sender.contains(sender))

        # Apply pagination and ordering
        emails = query.order_by(EmailLog.received_at.desc()).offset(skip).limit(limit).all()

        return emails
    except Exception as e:
        logger.error(f"Error getting emails: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/emails/{email_id}", response_model=EmailLogDetail)
def get_email(email_id: int, db: Session = Depends(get_db)):
    """Get a specific email by ID"""
    try:
        email = db.query(EmailLog).filter(EmailLog.id == email_id).first()

        if not email:
            raise HTTPException(status_code=404, detail=f"Email with ID {email_id} not found")

        return email
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting email {email_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/notifications", response_model=List[NotificationResponse])
def get_notifications(
    status: Optional[str] = Query(None, description="Filter by status"),
    recipient: Optional[str] = Query(None, description="Filter by recipient"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_db)
):
    """Get all WhatsApp notifications with optional filtering"""
    try:
        query = db.query(WhatsAppNotification)

        # Apply filters
        if status:
            query = query.filter(WhatsAppNotification.status == status)
        if recipient:
            query = query.filter(WhatsAppNotification.recipient.contains(recipient))

        # Apply pagination and ordering
        notifications = query.order_by(WhatsAppNotification.sent_at.desc()).offset(skip).limit(limit).all()

        return notifications
    except Exception as e:
        logger.error(f"Error getting notifications: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/replies", response_model=List[ReplyResponse])
def get_replies(
    status: Optional[str] = Query(None, description="Filter by status"),
    reply_to: Optional[str] = Query(None, description="Filter by reply_to"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_db)
):
    """Get all email replies with optional filtering"""
    try:
        query = db.query(EmailReply)

        # Apply filters
        if status:
            query = query.filter(EmailReply.status == status)
        if reply_to:
            query = query.filter(EmailReply.reply_to.contains(reply_to))

        # Apply pagination and ordering
        replies = query.order_by(EmailReply.sent_at.desc()).offset(skip).limit(limit).all()

        return replies
    except Exception as e:
        logger.error(f"Error getting replies: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/sms-notifications", response_model=List[SMSNotificationResponse])
def get_sms_notifications(
    status: Optional[str] = Query(None, description="Filter by status"),
    recipient: Optional[str] = Query(None, description="Filter by recipient"),
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=1000, description="Number of records to return"),
    db: Session = Depends(get_db)
):
    """Get all SMS notifications with optional filtering"""
    try:
        query = db.query(SMSNotification)

        # Apply filters
        if status:
            query = query.filter(SMSNotification.status == status)
        if recipient:
            query = query.filter(SMSNotification.recipient.contains(recipient))

        # Apply pagination and ordering
        sms_notifications = query.order_by(SMSNotification.sent_at.desc()).offset(skip).limit(limit).all()

        return sms_notifications
    except Exception as e:
        logger.error(f"Error getting SMS notifications: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/stats", response_model=StatsResponse)
def get_stats(db: Session = Depends(get_db)):
    """Get system statistics"""
    try:
        # Import func from sqlalchemy
        from sqlalchemy import func

        # Count emails by status
        email_stats = db.query(EmailLog.status, func.count(EmailLog.id)).group_by(EmailLog.status).all()
        email_status_counts = {status: count for status, count in email_stats}
        total_emails = db.query(EmailLog).count()

        # Count notifications by status
        notification_stats = db.query(WhatsAppNotification.status, func.count(WhatsAppNotification.id)).group_by(WhatsAppNotification.status).all()
        notification_status_counts = {status: count for status, count in notification_stats}
        total_notifications = db.query(WhatsAppNotification).count()

        # Count SMS notifications by status
        sms_stats = db.query(SMSNotification.status, func.count(SMSNotification.id)).group_by(SMSNotification.status).all()
        sms_status_counts = {status: count for status, count in sms_stats}
        total_sms = db.query(SMSNotification).count()

        # Count replies by status
        reply_stats = db.query(EmailReply.status, func.count(EmailReply.id)).group_by(EmailReply.status).all()
        reply_status_counts = {status: count for status, count in reply_stats}
        total_replies = db.query(EmailReply).count()

        return StatsResponse(
            emails=StatusCount(total=total_emails, by_status=email_status_counts),
            notifications=StatusCount(total=total_notifications, by_status=notification_status_counts),
            sms_notifications=StatusCount(total=total_sms, by_status=sms_status_counts),
            replies=StatusCount(total=total_replies, by_status=reply_status_counts)
        )
    except Exception as e:
        logger.error(f"Error getting stats: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

# Employee Management Models
class EmployeeCreate(BaseModel):
    name: str
    phone: str

class EmployeeUpdate(BaseModel):
    name: Optional[str] = None
    phone: Optional[str] = None

class EmployeeResponse(BaseModel):
    id: int
    name: str
    phone: str
    status: str = "active"
    created_at: str

    class Config:
        from_attributes = True

# In-memory employee storage (for demo purposes)
employees_db = [
    {"id": 1, "name": "Vishnu Bala Guru", "phone": "+917598638873", "status": "active", "created_at": "2024-01-01T00:00:00"},
    {"id": 2, "name": "John Doe", "phone": "+919778869983", "status": "active", "created_at": "2024-01-02T00:00:00"},
    {"id": 3, "name": "karthik", "phone": "+919788002618", "status": "active", "created_at": "2024-01-03T00:00:00"},
]
next_employee_id = 4

@app.get("/api/employees", response_model=List[EmployeeResponse])
def get_employees():
    """Get all employees"""
    return employees_db

@app.post("/api/employees", response_model=EmployeeResponse)
def create_employee(employee: EmployeeCreate):
    """Create a new employee"""
    global next_employee_id

    from datetime import datetime

    new_employee = {
        "id": next_employee_id,
        "name": employee.name,
        "phone": employee.phone,
        "status": "active",
        "created_at": datetime.now().isoformat()
    }

    employees_db.append(new_employee)
    next_employee_id += 1

    return new_employee

@app.get("/api/employees/{employee_id}", response_model=EmployeeResponse)
def get_employee(employee_id: int):
    """Get a specific employee by ID"""
    employee = next((emp for emp in employees_db if emp["id"] == employee_id), None)
    if not employee:
        raise HTTPException(status_code=404, detail="Employee not found")
    return employee

@app.put("/api/employees/{employee_id}", response_model=EmployeeResponse)
def update_employee(employee_id: int, employee: EmployeeUpdate):
    """Update an employee"""
    emp_index = next((i for i, emp in enumerate(employees_db) if emp["id"] == employee_id), None)
    if emp_index is None:
        raise HTTPException(status_code=404, detail="Employee not found")

    if employee.name is not None:
        employees_db[emp_index]["name"] = employee.name
    if employee.phone is not None:
        employees_db[emp_index]["phone"] = employee.phone

    return employees_db[emp_index]

@app.delete("/api/employees/{employee_id}")
def delete_employee(employee_id: int):
    """Delete an employee"""
    global employees_db
    employees_db = [emp for emp in employees_db if emp["id"] != employee_id]
    return {"message": "Employee deleted successfully"}

# Get the current directory
import pathlib
current_dir = pathlib.Path(__file__).parent.parent

# Serve frontend static files
try:
    frontend_dir = current_dir / "frontend"
    if frontend_dir.exists():
        app.mount("/static", StaticFiles(directory=str(frontend_dir)), name="static")
        print(f"✅ Mounted static files from: {frontend_dir}")
    else:
        print(f"❌ Frontend directory not found: {frontend_dir}")
except Exception as e:
    print(f"❌ Error mounting static files: {e}")

@app.get("/dashboard")
def serve_dashboard():
    """Serve the dashboard frontend"""
    # Try the new simplified dashboard first
    dashboard_file = current_dir / "frontend" / "dashboard.html"
    if dashboard_file.exists():
        return FileResponse(str(dashboard_file))

    # Fallback to original
    frontend_file = current_dir / "frontend" / "index.html"
    if frontend_file.exists():
        return FileResponse(str(frontend_file))
    else:
        raise HTTPException(status_code=404, detail=f"Frontend file not found: {frontend_file}")

@app.get("/frontend")
def serve_frontend():
    """Serve the frontend"""
    dashboard_file = current_dir / "frontend" / "dashboard.html"
    if dashboard_file.exists():
        return FileResponse(str(dashboard_file))

    frontend_file = current_dir / "frontend" / "index.html"
    if frontend_file.exists():
        return FileResponse(str(frontend_file))
    else:
        raise HTTPException(status_code=404, detail=f"Frontend file not found: {frontend_file}")

# Serve individual static files as fallback
@app.get("/css/{file_path:path}")
def serve_css(file_path: str):
    """Serve CSS files"""
    css_file = current_dir / "frontend" / "css" / file_path
    if css_file.exists():
        return FileResponse(str(css_file))
    else:
        raise HTTPException(status_code=404, detail=f"CSS file not found: {css_file}")

@app.get("/js/{file_path:path}")
def serve_js(file_path: str):
    """Serve JavaScript files"""
    js_file = current_dir / "frontend" / "js" / file_path
    if js_file.exists():
        return FileResponse(str(js_file))
    else:
        raise HTTPException(status_code=404, detail=f"JS file not found: {js_file}")
