#!/usr/bin/env python3
"""
Debug script to test SMS service initialization and sending
"""

import sys
import os
sys.path.append('.')

def test_sms_service_init():
    """Test SMS service initialization"""
    print("🔧 Testing SMS Service Initialization...")
    
    try:
        from app.worker import get_config
        from app.services.vonage_sms import get_sms_service
        
        config = get_config()
        print(f"SMS Enabled: {config.get('sms_enabled')}")
        print(f"SMS Provider: {config.get('sms_provider')}")
        print(f"Vonage SMS API Key: {config.get('vonage_sms_api_key', 'Not set')}")
        print(f"Vonage SMS API Secret: {config.get('vonage_sms_api_secret', 'Not set')}")
        
        sms_service = get_sms_service(config)
        print(f"SMS Service Type: {type(sms_service).__name__}")
        
        if hasattr(sms_service, 'api_key'):
            print(f"SMS Service API Key: {sms_service.api_key}")
        
        return sms_service
        
    except Exception as e:
        print(f"❌ Error initializing SMS service: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_notification_service():
    """Test notification service SMS integration"""
    print("\n🔧 Testing Notification Service...")
    
    try:
        from app.worker import get_config
        from app.services.notification_service import get_notification_service
        
        config = get_config()
        notification_service = get_notification_service(config)
        
        print(f"Notification Service Type: {type(notification_service).__name__}")
        print(f"SMS Service in Notification Service: {type(notification_service.sms_service).__name__}")
        
        if hasattr(notification_service.sms_service, 'api_key'):
            print(f"SMS Service API Key: {notification_service.sms_service.api_key}")
        
        return notification_service
        
    except Exception as e:
        print(f"❌ Error initializing notification service: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def test_sms_sending():
    """Test actual SMS sending"""
    print("\n📱 Testing SMS Sending...")
    
    try:
        from app.models import EmailLog, get_db
        from app.worker import get_config
        from app.services.vonage_sms import get_sms_service
        
        config = get_config()
        sms_service = get_sms_service(config)
        db = get_db()
        
        # Create a test email log
        test_email = EmailLog(
            message_id="test-sms-debug-001",
            sender="<EMAIL>",
            recipient="<EMAIL>",
            subject="SMS Debug Test",
            whatsapp_summary="This is a test email for SMS debugging.",
            status="processed"
        )
        
        db.add(test_email)
        db.commit()
        print(f"Created test email with ID: {test_email.id}")
        
        # Test SMS sending
        if hasattr(sms_service, 'send_notifications'):
            print("Calling send_notifications...")
            sms_results = sms_service.send_notifications(test_email, db)
            print(f"SMS Results: {len(sms_results)} notifications")
            
            for result in sms_results:
                print(f"  - {result.employee_name}: {result.status}")
                if result.error_message:
                    print(f"    Error: {result.error_message}")
        else:
            print("SMS service doesn't have send_notifications method")
        
        # Cleanup
        db.query(test_email.__class__).filter(test_email.__class__.id == test_email.id).delete()
        db.commit()
        print("Cleaned up test data")
        
    except Exception as e:
        print(f"❌ Error testing SMS sending: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧪 SMS Debug Test")
    print("=" * 50)
    
    # Test 1: SMS Service Initialization
    sms_service = test_sms_service_init()
    
    # Test 2: Notification Service
    notification_service = test_notification_service()
    
    # Test 3: SMS Sending
    if sms_service:
        test_sms_sending()
    
    print("\n✅ SMS Debug Test Complete!")
