# Vonage Voice Calling Integration

This document explains how to set up and use the Vonage Voice API integration for making voice calls to employees when email notifications are received.

## 🎯 **Features**

### **Automatic Voice Calls**
- ✅ **Triggers voice calls** to employees when WhatsApp notifications are sent
- ✅ **Individual calls** to each active employee
- ✅ **Personalized messages** with employee names and email details
- ✅ **Automatic retry logic** for failed or unanswered calls

### **Smart Retry System**
- ✅ **10-minute delays** for rescheduling failed calls
- ✅ **Maximum 3 retries** per call to avoid spam
- ✅ **Background monitoring** for automatic retries
- ✅ **Permanent failure** marking after max retries

### **Comprehensive Analytics**
- ✅ **Call tracking** with detailed logs in `voice_call_logs` table
- ✅ **Cost monitoring** and billing analytics
- ✅ **Quality metrics** and performance tracking
- ✅ **Employee-specific** call statistics

## 🚀 **Setup Instructions**

### **1. Vonage Account Setup**

1. **Create Account**: Sign up at [vonage.com](https://vonage.com)
2. **Get API Credentials**: 
   - Go to your dashboard and note your API Key and Secret
3. **Create Voice Application**:
   - Navigate to Applications in your dashboard
   - Create a new Voice application
   - Note the Application ID
   - Download the private key (optional for JWT auth)
4. **Get Phone Number**:
   - Purchase or configure a phone number for outbound calls
   - Note the phone number in international format

### **2. Environment Configuration**

Add these variables to your `.env` file:

```env
# Voice calling with Vonage
VOICE_CALLING_ENABLED=True
VOICE_PROVIDER=vonage
VONAGE_API_KEY=your_vonage_api_key
VONAGE_API_SECRET=your_vonage_api_secret
VONAGE_APPLICATION_ID=your_vonage_application_id
VONAGE_PRIVATE_KEY=your_vonage_private_key  # Optional
VONAGE_FROM_NUMBER=+**********  # Your Vonage phone number
```

### **3. Database Migration**
The system will automatically create the `voice_call_logs` table when you start the application. The table includes:
- Call tracking information
- Vonage call UUIDs
- Status and timing data
- Retry scheduling
- Error logging

## 🔧 **How It Works**

### **Call Flow**
1. **Email Received** → System processes and summarizes email
2. **WhatsApp Sent** → Notifications sent to team members
3. **Voice Calls Triggered** → Automatic calls to active employees
4. **Call Analytics** → Detailed tracking and logging

### **Call Message Format**
The system generates personalized voice messages:

```
"Hello [Employee Name], this is an automated notification from the Email Monitor Agent. 
You have received an important email from [Sender Name]. 
The subject is: [Email Subject]. 
Summary: [Email Summary]. 
Please check your email for full details. Thank you."
```

### **Call Retry Logic**
- **Failed calls** are automatically retried up to 3 times
- **10-minute intervals** between retry attempts
- **Background service** monitors and processes retries
- **Permanent failure** status after 3 failed attempts

### **Employee Status**
- Only **active employees** receive calls
- Employee data fetched from the main API
- **Phone numbers** must include country codes (e.g., +**********)

## 🔧 **API Endpoints**

### **Manual Call Retry**
```http
POST /api/calls/retry
```
Manually trigger retry of failed voice calls.

### **Voice Call Analytics**
```http
GET /api/voice-calls/analytics?hours=24
```
Get comprehensive call analytics including success rates, costs, and quality metrics.

### **Employee Call Statistics**
```http
GET /api/voice-calls/employee-stats?employee_id=123&hours=24
```
Get call statistics grouped by employee.

### **Call Logs**
```http
GET /api/voice-calls/logs?status=completed&limit=50&offset=0
```
Get filtered call logs with pagination.

## 📊 **Analytics & Monitoring**

### **Call Metrics**
- **Answer Rate**: Percentage of calls answered
- **Completion Rate**: Percentage of calls completed successfully
- **Average Duration**: Mean call duration
- **Cost Per Call**: Average cost per call
- **Quality Distribution**: Call quality breakdown

### **Employee Performance**
- **Calls per Employee**: Volume by employee
- **Employee Answer Rates**: Individual performance
- **Cost by Employee**: Spending analysis

### **Real-time Monitoring**
- **Failed Call Alerts**: Monitor for failed calls
- **Cost Thresholds**: Set up cost monitoring
- **Quality Tracking**: Monitor call quality trends

## 🛠️ **Troubleshooting**

### **Common Issues**

1. **Calls Not Initiating**
   - Check Vonage API credentials
   - Verify `VOICE_CALLING_ENABLED=True`
   - Ensure phone numbers include country codes

2. **Authentication Errors**
   - Verify API Key and Secret
   - Check Application ID
   - Ensure sufficient account balance

3. **Call Quality Issues**
   - Check network connectivity
   - Verify phone number format
   - Monitor Vonage service status

### **Logs and Debugging**
- Check application logs for detailed error messages
- Monitor the `voice_call_logs` table for call status
- Use Vonage dashboard for call analytics

## 💡 **Best Practices**

### **Cost Management**
- **Monitor Usage**: Set up cost alerts in Vonage dashboard
- **Limit Retries**: Keep retry count reasonable (max 3)
- **Track Metrics**: Regular review of call analytics

### **Quality Optimization**
- **Test Calls**: Regularly test with different phone numbers
- **Message Length**: Keep voice messages concise but informative
- **Timing**: Consider time zones for international calls

### **Security**
- **Secure Credentials**: Keep API keys secure and rotate regularly
- **Access Control**: Limit who can modify voice calling settings
- **Audit Logs**: Regular review of call logs and analytics

The Vonage integration provides robust voice calling capabilities with comprehensive analytics and monitoring for optimizing employee notification effectiveness.
