# Action Monitoring System

This document explains the comprehensive action monitoring system that tracks all activities in the Email Monitor Agent.

## 🎯 **Overview**

The Action Monitoring System provides complete visibility into all system activities, including:
- **Email processing** events
- **WhatsApp notifications** sent
- **Voice calls** made via VAPI
- **Email replies** sent
- **Employee management** actions
- **System events** and API requests

## 📊 **Features**

### **Comprehensive Tracking**
- ✅ **All system actions** logged with detailed metadata
- ✅ **Real-time monitoring** with automatic refresh
- ✅ **Performance metrics** including duration and success rates
- ✅ **Cost tracking** for paid services (VAPI calls, etc.)
- ✅ **Error logging** with detailed error messages

### **Advanced Analytics**
- ✅ **Success rate calculations** over time periods
- ✅ **Action type breakdowns** with visual charts
- ✅ **Status distribution** analysis
- ✅ **Cost analysis** for budget monitoring
- ✅ **Performance trends** and bottleneck identification

### **Smart Filtering**
- ✅ **Filter by action type** (email, WhatsApp, voice call, etc.)
- ✅ **Filter by status** (success, failed, pending, etc.)
- ✅ **Time-based filtering** (last 24 hours, week, month)
- ✅ **Employee-specific** action tracking
- ✅ **Email-specific** action chains

## 🗄️ **Database Schema**

### **ActionMonitor Table**
```sql
CREATE TABLE action_monitors (
    id INTEGER PRIMARY KEY,
    email_log_id INTEGER REFERENCES email_logs(id),
    action_type VARCHAR(50) NOT NULL,
    action_category VARCHAR(50) NOT NULL,
    action_description TEXT NOT NULL,
    target_recipient VARCHAR(255),
    employee_name VARCHAR(255),
    status VARCHAR(50) NOT NULL,

    -- Timing
    initiated_at DATETIME NOT NULL,
    completed_at DATETIME,
    duration_seconds INTEGER,

    -- Results
    success VARCHAR(10) NOT NULL DEFAULT 'pending',
    error_message TEXT,
    response_data TEXT,

    -- Cost and retries
    cost FLOAT,
    retry_count INTEGER DEFAULT 0,
    next_retry_at DATETIME,

    -- Metadata
    user_agent VARCHAR(255),
    ip_address VARCHAR(45),
    additional_data TEXT
);
```

## 📋 **Action Types**

| Type | Description | Category |
|------|-------------|----------|
| `email_received` | New email received | processing |
| `email_processed` | Email processing completed | processing |
| `whatsapp_sent` | WhatsApp notification sent | notification |
| `voice_call` | VAPI voice call made | communication |
| `email_reply` | Email reply sent | communication |
| `employee_added` | New employee added | management |
| `employee_updated` | Employee information updated | management |
| `employee_deleted` | Employee removed | management |
| `system_startup` | System started | system |
| `api_request` | API endpoint called | api |
| `background_task` | Background process executed | system |

## 🔄 **Status Types**

| Status | Description |
|--------|-------------|
| `pending` | Action created but not started |
| `in_progress` | Action currently executing |
| `success` | Action completed successfully |
| `failed` | Action failed (may retry) |
| `pending_retry` | Scheduled for retry |
| `failed_permanently` | Failed after max retries |

## 🚀 **API Endpoints**

### **Get Actions**
```http
GET /api/actions?action_type=voice_call&status=success&limit=50&offset=0
```
Retrieve action records with optional filtering.

**Parameters:**
- `action_type`: Filter by action type
- `action_category`: Filter by category
- `status`: Filter by status
- `email_log_id`: Filter by email ID
- `limit`: Maximum results (default: 100)
- `offset`: Pagination offset (default: 0)

### **Get Statistics**
```http
GET /api/actions/statistics?hours=24
```
Get aggregated statistics for the specified time period.

**Parameters:**
- `hours`: Time period in hours (default: 24)

**Response:**
```json
{
    "period_hours": 24,
    "total_actions": 156,
    "successful_actions": 142,
    "success_rate": 91.03,
    "total_cost": 2.45,
    "average_duration_seconds": 3.2,
    "actions_by_type": {
        "email_processed": 12,
        "whatsapp_sent": 36,
        "voice_call": 36,
        "email_reply": 24
    },
    "actions_by_status": {
        "success": 142,
        "failed": 8,
        "pending": 6
    }
}
```

### **Get Specific Action**
```http
GET /api/actions/{action_id}
```
Retrieve details for a specific action.

## 🎨 **Frontend Dashboard**

### **Monitoring Tab Features**
- ✅ **Real-time statistics** cards showing key metrics
- ✅ **Action type breakdown** with visual progress bars
- ✅ **Status distribution** charts
- ✅ **Recent actions table** with filtering
- ✅ **Auto-refresh** every 30 seconds
- ✅ **Responsive design** for all screen sizes

### **Statistics Cards**
1. **Successful Actions** - Count of successful actions in last 24h
2. **Failed Actions** - Count of failed actions in last 24h
3. **Success Rate** - Percentage of successful actions
4. **Total Cost** - Sum of all action costs in last 24h

### **Interactive Features**
- **Filter by type**: Email, WhatsApp, Voice Call, etc.
- **Filter by status**: Success, Failed, Pending, etc.
- **Real-time updates**: Automatic refresh of data
- **Detailed view**: Click actions for full details

## 🔧 **Integration Points**

### **Notification Service Integration**
```python
# Log notification process start
action_monitor = get_action_monitor_service(db_session)
notification_action = action_monitor.log_action(
    action_type=ActionTypes.EMAIL_PROCESSED,
    action_category=ActionCategories.PROCESSING,
    action_description=f"Starting notification process for email: {email_log.subject}",
    email_log_id=email_log.id,
    status='in_progress'
)

# Complete the action
action_monitor.complete_action(
    notification_action.id,
    success=True,
    response_data={'whatsapp_count': 3, 'voice_call_count': 3}
)
```

### **Vonage Integration**
```python
# Log voice call initiation
call_action = action_monitor.log_action(
    action_type=ActionTypes.VOICE_CALL,
    action_category=ActionCategories.COMMUNICATION,
    action_description=f"Voice call to {employee_name} about email: {email_subject}",
    target_recipient=phone_number,
    employee_name=employee_name,
    email_log_id=email_log.id
)

# Complete with call results
action_monitor.complete_action(
    call_action.id,
    success=call_result['success'],
    cost=call_result.get('cost'),
    response_data=call_result
)
```

## 📈 **Performance Benefits**

### **Operational Visibility**
- **Complete audit trail** of all system activities
- **Performance bottleneck** identification
- **Error pattern** analysis
- **Cost optimization** insights

### **Troubleshooting**
- **Detailed error logs** for failed actions
- **Timing analysis** for slow operations
- **Retry tracking** for reliability monitoring
- **Integration health** monitoring

### **Business Intelligence**
- **Usage patterns** analysis
- **Cost tracking** and budgeting
- **Success rate** trends
- **Employee engagement** metrics

## 🔍 **Monitoring Best Practices**

### **Regular Review**
1. **Daily**: Check success rates and failed actions
2. **Weekly**: Review cost trends and performance
3. **Monthly**: Analyze usage patterns and optimization opportunities

### **Alert Thresholds**
- **Success rate** below 90%
- **Failed actions** exceeding 10% of total
- **Cost** exceeding budget thresholds
- **Response time** degradation

### **Data Retention**
- **Recent data**: Keep detailed logs for 30 days
- **Historical data**: Aggregate monthly summaries
- **Archive**: Long-term storage for compliance

## 🛠️ **Maintenance**

### **Database Cleanup**
```sql
-- Clean up old action records (older than 90 days)
DELETE FROM action_monitors
WHERE initiated_at < NOW() - INTERVAL 90 DAY;

-- Archive old records before deletion
INSERT INTO action_monitors_archive
SELECT * FROM action_monitors
WHERE initiated_at < NOW() - INTERVAL 90 DAY;
```

### **Performance Optimization**
- **Index** on frequently queried columns
- **Partition** by date for large datasets
- **Aggregate** old data for reporting
- **Cache** frequently accessed statistics

## 🔐 **Security Considerations**

- **Sensitive data** (phone numbers, emails) properly masked in logs
- **Access control** for monitoring dashboard
- **Data retention** policies for compliance
- **Audit logging** for monitoring system access

The Action Monitoring System provides comprehensive visibility into your Email Monitor Agent, enabling proactive management, troubleshooting, and optimization of all system activities.
