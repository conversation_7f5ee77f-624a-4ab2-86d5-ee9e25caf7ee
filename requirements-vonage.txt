# Vonage Voice API Integration Requirements
# Install with: pip install -r requirements-vonage.txt

# Core Vonage Voice API
vonage==3.14.0

# HTTP requests (required for Vonage API calls)
requests>=2.25.0
httpx>=0.24.0

# JWT for Vonage authentication
PyJWT>=2.0.0

# Cryptography for secure communications
cryptography>=3.0.0

# JSON Web Token handling
python-jose[cryptography]>=3.3.0

# Additional networking utilities
urllib3>=1.26.0
certifi>=2021.0.0

# Base64 encoding/decoding utilities
base64io>=1.0.0

# Date and time utilities for call scheduling
python-dateutil>=2.8.0

# Environment variable management
python-dotenv>=0.19.0

# Logging utilities
structlog>=21.0.0

# Optional: Audio processing (if needed for call quality analysis)
# pydub>=0.25.0
# scipy>=1.7.0

# Optional: Async support for high-volume calling
# aiohttp>=3.8.0
# asyncio-mqtt>=0.11.0
