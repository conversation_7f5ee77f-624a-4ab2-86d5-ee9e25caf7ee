#!/usr/bin/env python3
"""
Direct test of Vonage SMS API to troubleshoot SMS issues
"""

import os
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_vonage_sms_api():
    """Test Vonage SMS API directly"""
    print("🔍 Testing Vonage SMS API Directly")
    print("=" * 50)
    
    # Get configuration from environment
    api_key = os.getenv('VONAGE_SMS_API_KEY')
    api_secret = os.getenv('VONAGE_SMS_API_SECRET')
    from_number = os.getenv('VONAGE_SMS_FROM_NUMBER', 'EmailAgent')
    
    print(f"API Key: {api_key[:8]}..." if api_key else "❌ Not set")
    print(f"API Secret: {api_secret[:8]}..." if api_secret else "❌ Not set")
    print(f"From Number: {from_number}")
    
    if not api_key or not api_secret:
        print("❌ Vonage SMS credentials not properly configured!")
        return False
    
    # Test numbers from dashboard
    test_numbers = [
        "+917598638873",  # Vishnu
        "+919778869983",  # John
        "+919788002618"   # karthik
    ]
    
    print(f"\n📱 Testing SMS to {len(test_numbers)} numbers...")
    
    success_count = 0
    failed_count = 0
    
    for phone_number in test_numbers:
        print(f"\n📤 Sending SMS to {phone_number}...")
        
        # Vonage SMS API payload
        payload = {
            "from": from_number,
            "to": phone_number,
            "text": f"Test SMS from Email Monitor Agent. Time: {os.popen('date /t & time /t').read().strip()}",
            "api_key": api_key,
            "api_secret": api_secret
        }
        
        try:
            response = requests.post(
                "https://rest.nexmo.com/sms/json",
                data=payload,
                timeout=30
            )
            
            print(f"   📊 Response Status: {response.status_code}")
            
            if response.status_code == 200:
                sms_data = response.json()
                print(f"   📋 Response Data: {json.dumps(sms_data, indent=2)}")
                
                messages = sms_data.get('messages', [])
                if messages:
                    message = messages[0]
                    status = message.get('status')
                    message_id = message.get('message-id')
                    error_text = message.get('error-text')
                    
                    if status == '0':  # Success
                        print(f"   ✅ SMS sent successfully!")
                        print(f"   🆔 Message ID: {message_id}")
                        success_count += 1
                    else:
                        print(f"   ❌ SMS failed!")
                        print(f"   📋 Status Code: {status}")
                        print(f"   📝 Error: {error_text}")
                        failed_count += 1
                else:
                    print(f"   ❌ No messages in response")
                    failed_count += 1
            else:
                print(f"   ❌ HTTP Error: {response.status_code}")
                print(f"   📝 Response: {response.text}")
                failed_count += 1
                
        except Exception as e:
            print(f"   ❌ Exception: {str(e)}")
            failed_count += 1
    
    print(f"\n📊 Final Results:")
    print(f"   ✅ Successful: {success_count}")
    print(f"   ❌ Failed: {failed_count}")
    print(f"   📱 Total: {len(test_numbers)}")
    
    return success_count > 0

def test_vonage_account_balance():
    """Check Vonage account balance"""
    print("\n💰 Checking Vonage Account Balance...")
    
    api_key = os.getenv('VONAGE_SMS_API_KEY')
    api_secret = os.getenv('VONAGE_SMS_API_SECRET')
    
    try:
        response = requests.get(
            f"https://rest.nexmo.com/account/get-balance?api_key={api_key}&api_secret={api_secret}",
            timeout=10
        )
        
        if response.status_code == 200:
            balance_data = response.json()
            balance = balance_data.get('value', 'Unknown')
            currency = balance_data.get('currency', 'EUR')
            print(f"   💰 Account Balance: {balance} {currency}")
            
            if float(balance) <= 0:
                print(f"   ⚠️  WARNING: Low or zero balance! Please top up your Vonage account.")
                return False
            else:
                print(f"   ✅ Balance is sufficient for SMS sending")
                return True
        else:
            print(f"   ❌ Failed to get balance: {response.status_code}")
            print(f"   📝 Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ Error checking balance: {str(e)}")
        return False

def test_vonage_pricing():
    """Check SMS pricing for Indian numbers"""
    print("\n💸 Checking SMS Pricing for India...")
    
    api_key = os.getenv('VONAGE_SMS_API_KEY')
    api_secret = os.getenv('VONAGE_SMS_API_SECRET')
    
    try:
        response = requests.get(
            f"https://rest.nexmo.com/account/get-pricing/outbound/sms?api_key={api_key}&api_secret={api_secret}&country=IN",
            timeout=10
        )
        
        if response.status_code == 200:
            pricing_data = response.json()
            print(f"   📋 Pricing Data: {json.dumps(pricing_data, indent=2)}")
        else:
            print(f"   ❌ Failed to get pricing: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ Error checking pricing: {str(e)}")

def main():
    """Main function"""
    print("🚀 Vonage SMS Direct Test")
    print("=" * 50)
    
    # Test account balance first
    balance_ok = test_vonage_account_balance()
    
    # Check pricing
    test_vonage_pricing()
    
    # Test SMS sending
    if balance_ok:
        sms_success = test_vonage_sms_api()
        
        if sms_success:
            print("\n🎉 SMS Test Successful!")
            print("   📱 Check your phones for received messages")
        else:
            print("\n❌ SMS Test Failed!")
            print("   🔧 Please check your Vonage configuration")
    else:
        print("\n⚠️  Skipping SMS test due to account balance issues")
    
    print("\n💡 Troubleshooting Tips:")
    print("   • Ensure Vonage account has sufficient balance")
    print("   • Verify API credentials are correct")
    print("   • Check if phone numbers are in correct international format")
    print("   • Ensure SMS service is enabled for your Vonage account")

if __name__ == "__main__":
    main()
