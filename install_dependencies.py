#!/usr/bin/env python3
"""
Dependency Installation Script for Email Monitor Agent with Vonage Integration
"""

import subprocess
import sys
import os

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"   Command: {command}")
        print(f"   Error: {e.stderr}")
        return False

def install_requirements():
    """Install all required dependencies"""
    print("🚀 Installing Email Monitor Agent Dependencies")
    print("=" * 60)
    
    # Core dependencies for Vonage integration
    vonage_deps = [
        "vonage==3.14.0",
        "requests==2.32.3",
        "httpx==0.28.1",
        "PyJWT==2.10.1",
        "cryptography>=3.0.0"
    ]
    
    # Database dependencies
    db_deps = [
        "SQLAlchemy==2.0.41",
        "psycopg2-binary==2.9.9",
        "asyncpg==0.29.0"
    ]
    
    # FastAPI and web dependencies
    web_deps = [
        "fastapi==0.115.12",
        "uvicorn==0.34.2",
        "starlette==0.46.2",
        "pydantic==2.11.4",
        "pydantic_core==2.33.2"
    ]
    
    # Email processing dependencies
    email_deps = [
        "imaplib2==3.6",
        "email_validator==2.2.0"
    ]
    
    # AI and OpenAI dependencies
    ai_deps = [
        "openai==1.82.0"
    ]
    
    # Utility dependencies
    util_deps = [
        "python-dotenv==1.1.0",
        "python-dateutil==2.9.0.post0",
        "typing_extensions==4.13.2",
        "PyYAML==6.0.2",
        "click==8.1.8",
        "Jinja2==3.1.6",
        "MarkupSafe==3.0.2"
    ]
    
    # Testing dependencies
    test_deps = [
        "pytest==8.3.5",
        "pytest-mock==3.14.0"
    ]
    
    # Networking dependencies
    network_deps = [
        "aiohttp==3.11.18",
        "aiosignal==1.3.2",
        "anyio==4.9.0",
        "certifi>=2023.0.0",
        "charset-normalizer==3.4.2",
        "idna==3.10",
        "urllib3==2.4.0"
    ]
    
    # Data processing (optional but useful)
    data_deps = [
        "pandas==2.2.3",
        "numpy==2.2.6"
    ]
    
    # Core Python utilities
    core_deps = [
        "six==1.17.0",
        "packaging>=20.0",
        "setuptools",
        "wheel"
    ]
    
    all_deps = [
        ("Vonage Voice API", vonage_deps),
        ("Database", db_deps),
        ("FastAPI Web Framework", web_deps),
        ("Email Processing", email_deps),
        ("AI/OpenAI", ai_deps),
        ("Utilities", util_deps),
        ("Testing", test_deps),
        ("Networking", network_deps),
        ("Data Processing", data_deps),
        ("Core Python", core_deps)
    ]
    
    success_count = 0
    total_count = len(all_deps)
    
    for category, deps in all_deps:
        print(f"\n📦 Installing {category} dependencies...")
        
        for dep in deps:
            if run_command(f"pip install {dep}", f"Installing {dep}"):
                success_count += 1
            else:
                print(f"⚠️  Failed to install {dep}, continuing...")
    
    print(f"\n📊 Installation Summary:")
    print(f"   Categories processed: {total_count}")
    print(f"   Individual packages: {sum(len(deps) for _, deps in all_deps)}")
    
    # Try installing from requirements.txt as backup
    print(f"\n🔄 Installing from requirements.txt as backup...")
    if os.path.exists("requirements.txt"):
        run_command("pip install -r requirements.txt", "Installing from requirements.txt")
    
    if os.path.exists("email_monitor_fastapi/requirements.txt"):
        run_command("pip install -r email_monitor_fastapi/requirements.txt", "Installing from email_monitor_fastapi/requirements.txt")
    
    return True

def verify_installation():
    """Verify that key dependencies are installed"""
    print(f"\n🔍 Verifying Installation...")
    
    key_packages = [
        "vonage",
        "fastapi", 
        "sqlalchemy",
        "psycopg2",
        "requests",
        "openai",
        "python-dotenv"
    ]
    
    verified = 0
    
    for package in key_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package}: Available")
            verified += 1
        except ImportError:
            print(f"❌ {package}: Not available")
    
    print(f"\n📊 Verification Results: {verified}/{len(key_packages)} packages verified")
    
    if verified == len(key_packages):
        print("🎉 All key dependencies are installed and available!")
        return True
    else:
        print("⚠️  Some dependencies are missing. You may need to install them manually.")
        return False

def main():
    """Main installation function"""
    print("🚀 Email Monitor Agent - Dependency Installation")
    print("=" * 60)
    print("This script will install all dependencies for Vonage integration")
    print("")
    
    # Check Python version
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        return False
    
    print(f"✅ Python version: {sys.version}")
    
    # Install dependencies
    if not install_requirements():
        print("❌ Dependency installation failed")
        return False
    
    # Verify installation
    if not verify_installation():
        print("⚠️  Some dependencies could not be verified")
    
    print("\n🎉 Installation completed!")
    print("\n📋 Next Steps:")
    print("1. Set up your Vonage API credentials in .env file")
    print("2. Configure PostgreSQL database")
    print("3. Run: python test_vonage_integration.py")
    print("4. Start the application: python run.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
