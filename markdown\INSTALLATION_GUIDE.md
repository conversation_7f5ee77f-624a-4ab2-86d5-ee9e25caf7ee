# Installation Guide - Email Monitor Agent with Vonage Integration

This guide will help you install all dependencies required for the Email Monitor Agent with Vonage Voice API integration.

## 🚀 **Quick Installation**

### **Option 1: Automated Installation (Recommended)**

Run the automated installation script:

```bash
python install_dependencies.py
```

This script will:
- ✅ Install all required dependencies
- ✅ Verify the installation
- ✅ Provide next steps

### **Option 2: Manual Installation**

#### **1. Install Core Dependencies**

```bash
# Install from main requirements file
pip install -r requirements.txt

# Or install from FastAPI requirements
pip install -r email_monitor_fastapi/requirements.txt
```

#### **2. Install Vonage-Specific Dependencies**

```bash
# Install Vonage-specific requirements
pip install -r requirements-vonage.txt
```

#### **3. Install Individual Packages**

If you prefer to install packages individually:

```bash
# Core Vonage Voice API
pip install vonage==3.14.0

# HTTP and networking
pip install requests==2.32.3 httpx==0.28.1

# Authentication and security
pip install PyJWT==2.10.1 python-jose[cryptography]==3.3.0 cryptography>=45.0.2

# Database (PostgreSQL)
pip install SQLAlchemy==2.0.41 psycopg2-binary==2.9.9 asyncpg==0.29.0

# FastAPI web framework
pip install fastapi==0.115.12 uvicorn==0.34.2 pydantic==2.11.4

# Email processing
pip install imaplib2==3.6 email_validator==2.2.0

# AI/OpenAI
pip install openai==1.82.0

# Utilities
pip install python-dotenv==1.1.0 python-dateutil==2.9.0.post0
```

## 🔧 **System Requirements**

### **Python Version**
- **Required**: Python 3.8 or higher
- **Recommended**: Python 3.10+

### **Operating System**
- ✅ Windows 10/11
- ✅ macOS 10.15+
- ✅ Linux (Ubuntu 18.04+, CentOS 7+)

### **Database**
- **PostgreSQL 12+** (recommended)
- SQLite (for development only)

## 📦 **Key Dependencies**

### **Vonage Voice API**
```
vonage==3.14.0                    # Main Vonage SDK
python-jose[cryptography]==3.3.0  # JWT authentication
PyJWT==2.10.1                     # Token handling
```

### **Web Framework**
```
fastapi==0.115.12                 # Web API framework
uvicorn==0.34.2                   # ASGI server
pydantic==2.11.4                  # Data validation
```

### **Database**
```
SQLAlchemy==2.0.41                # ORM
psycopg2-binary==2.9.9            # PostgreSQL adapter
asyncpg==0.29.0                   # Async PostgreSQL
```

### **Communication APIs**
```
requests==2.32.3                  # HTTP requests
httpx==0.28.1                     # Async HTTP
openai==1.82.0                    # AI summarization
```

## 🔍 **Verification**

### **Test Installation**

Run the verification script:

```bash
python test_vonage_integration.py
```

### **Manual Verification**

Test key imports:

```python
# Test Vonage
import vonage

# Test FastAPI
import fastapi

# Test Database
import sqlalchemy
import psycopg2

# Test HTTP
import requests
import httpx

# Test AI
import openai

print("✅ All dependencies installed successfully!")
```

## 🛠️ **Troubleshooting**

### **Common Issues**

#### **1. Vonage Installation Fails**
```bash
# Try upgrading pip first
pip install --upgrade pip setuptools wheel

# Then install Vonage
pip install vonage==3.14.0
```

#### **2. PostgreSQL Driver Issues**
```bash
# On Windows
pip install psycopg2-binary==2.9.9

# On Linux/macOS (if binary fails)
sudo apt-get install libpq-dev  # Ubuntu/Debian
brew install postgresql         # macOS
pip install psycopg2==2.9.9
```

#### **3. Cryptography Issues**
```bash
# Upgrade cryptography
pip install --upgrade cryptography

# On older systems
pip install cryptography==3.4.8
```

#### **4. FastAPI/Pydantic Compatibility**
```bash
# Install specific compatible versions
pip install fastapi==0.115.12 pydantic==2.11.4
```

### **Environment-Specific Issues**

#### **Virtual Environment (Recommended)**
```bash
# Create virtual environment
python -m venv email_monitor_env

# Activate (Windows)
email_monitor_env\Scripts\activate

# Activate (Linux/macOS)
source email_monitor_env/bin/activate

# Install dependencies
pip install -r requirements.txt
```

#### **Conda Environment**
```bash
# Create conda environment
conda create -n email_monitor python=3.10

# Activate
conda activate email_monitor

# Install dependencies
pip install -r requirements.txt
```

## 📋 **Next Steps**

After successful installation:

1. **Configure Environment Variables**:
   ```bash
   cp .env.example .env
   # Edit .env with your Vonage credentials
   ```

2. **Set Up Database**:
   ```bash
   python scripts/init_postgres.py
   ```

3. **Test Integration**:
   ```bash
   python test_vonage_integration.py
   ```

4. **Start Application**:
   ```bash
   python run.py
   ```

## 🆘 **Getting Help**

If you encounter issues:

1. **Check Python Version**: `python --version`
2. **Check Pip Version**: `pip --version`
3. **Update Pip**: `pip install --upgrade pip`
4. **Clear Cache**: `pip cache purge`
5. **Reinstall**: `pip install --force-reinstall -r requirements.txt`

For Vonage-specific issues, refer to the [Vonage Python SDK Documentation](https://developer.vonage.com/en/vonage-server-sdk/python).

## ✅ **Installation Complete**

Once all dependencies are installed, you're ready to configure and use the Email Monitor Agent with Vonage Voice API integration!
