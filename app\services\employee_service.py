"""
Shared employee service for managing employee data across all services.
This ensures consistency between the main API and worker API.
"""

import os
import json
import logging
from typing import List, Dict, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class EmployeeService:
    """
    Shared employee service that manages employee data.
    Uses a JSON file for persistence to ensure data survives server restarts.
    """
    
    def __init__(self, data_file: str = "employees_data.json"):
        """
        Initialize the employee service.
        
        Args:
            data_file: Path to the JSON file for storing employee data
        """
        self.data_file = data_file
        self.employees_db = []
        self.next_employee_id = 1
        self.load_data()
    
    def load_data(self):
        """Load employee data from JSON file"""
        try:
            if os.path.exists(self.data_file):
                with open(self.data_file, 'r') as f:
                    data = json.load(f)
                    self.employees_db = data.get('employees', [])
                    self.next_employee_id = data.get('next_id', 1)
                logger.info(f"Loaded {len(self.employees_db)} employees from {self.data_file}")
            else:
                # Initialize with default employees if file doesn't exist
                self.employees_db = [
                    {"id": 1, "name": "Vishnu Bala Guru", "phone": "+917598638873", "status": "active", "created_at": "2024-01-01T00:00:00"},
                    {"id": 2, "name": "John Doe", "phone": "+918778869983", "status": "active", "created_at": "2024-01-02T00:00:00"},
                    {"id": 3, "name": "karthik", "phone": "+919788002618", "status": "active", "created_at": "2024-01-03T00:00:00"},
                ]
                self.next_employee_id = 4
                self.save_data()
                logger.info("Initialized with default employee data")
        except Exception as e:
            logger.error(f"Error loading employee data: {e}")
            # Fallback to default data
            self.employees_db = [
                {"id": 1, "name": "Vishnu Bala Guru", "phone": "+917598638873", "status": "active", "created_at": "2024-01-01T00:00:00"},
                {"id": 2, "name": "John Doe", "phone": "+918778869983", "status": "active", "created_at": "2024-01-02T00:00:00"},
                {"id": 3, "name": "karthik", "phone": "+919788002618", "status": "active", "created_at": "2024-01-03T00:00:00"},
            ]
            self.next_employee_id = 4
    
    def save_data(self):
        """Save employee data to JSON file"""
        try:
            data = {
                'employees': self.employees_db,
                'next_id': self.next_employee_id
            }
            with open(self.data_file, 'w') as f:
                json.dump(data, f, indent=2)
            logger.info(f"Saved {len(self.employees_db)} employees to {self.data_file}")
        except Exception as e:
            logger.error(f"Error saving employee data: {e}")
    
    def get_all_employees(self) -> List[Dict]:
        """Get all employees"""
        return self.employees_db.copy()
    
    def get_employee(self, employee_id: int) -> Optional[Dict]:
        """Get a specific employee by ID"""
        return next((emp for emp in self.employees_db if emp["id"] == employee_id), None)
    
    def create_employee(self, name: str, phone: str) -> Dict:
        """Create a new employee"""
        new_employee = {
            "id": self.next_employee_id,
            "name": name,
            "phone": phone,
            "status": "active",
            "created_at": datetime.now().isoformat()
        }
        
        self.employees_db.append(new_employee)
        self.next_employee_id += 1
        self.save_data()
        
        logger.info(f"Created new employee: {name} ({phone})")
        return new_employee
    
    def update_employee(self, employee_id: int, name: Optional[str] = None, phone: Optional[str] = None) -> Optional[Dict]:
        """Update an employee"""
        emp_index = next((i for i, emp in enumerate(self.employees_db) if emp["id"] == employee_id), None)
        if emp_index is None:
            return None
        
        old_data = self.employees_db[emp_index].copy()
        
        if name is not None:
            self.employees_db[emp_index]["name"] = name
        if phone is not None:
            self.employees_db[emp_index]["phone"] = phone
        
        self.save_data()
        
        logger.info(f"Updated employee {employee_id}: {old_data} -> {self.employees_db[emp_index]}")
        return self.employees_db[emp_index]
    
    def delete_employee(self, employee_id: int) -> bool:
        """Delete an employee"""
        original_count = len(self.employees_db)
        self.employees_db = [emp for emp in self.employees_db if emp["id"] != employee_id]
        
        if len(self.employees_db) < original_count:
            self.save_data()
            logger.info(f"Deleted employee {employee_id}")
            return True
        return False
    
    def get_active_employees(self) -> List[Dict]:
        """Get only active employees"""
        return [emp for emp in self.employees_db if emp.get("status") == "active"]


# Global employee service instance
_employee_service = None

def get_employee_service() -> EmployeeService:
    """
    Get the global employee service instance.
    This ensures all parts of the application use the same employee data.
    """
    global _employee_service
    if _employee_service is None:
        _employee_service = EmployeeService()
    return _employee_service

def refresh_employee_service():
    """
    Refresh the employee service by reloading data from file.
    Useful when data might have been updated by another process.
    """
    global _employee_service
    if _employee_service is not None:
        _employee_service.load_data()
