# PostgreSQL Setup Guide for Email Monitor Agent

This guide will help you set up PostgreSQL for the Email Monitor Agent instead of SQLite.

## 🚀 Quick Start with Docker (Recommended)

### Option 1: Using Docker Compose (Easiest)

1. **Start PostgreSQL with Docker Compose:**
   ```bash
   docker-compose up -d postgres
   ```

2. **Wait for PostgreSQL to be ready:**
   ```bash
   docker-compose logs postgres
   ```

3. **Update your .env file:**
   ```env
   DATABASE_URL=postgresql://email_agent:secure_password_123@localhost:5432/email_monitor
   POSTGRES_HOST=localhost
   POSTGRES_PORT=5432
   POSTGRES_DB=email_monitor
   POSTGRES_USER=email_agent
   POSTGRES_PASSWORD=secure_password_123
   ```

4. **Initialize the database:**
   ```bash
   python scripts/init_postgres.py
   ```

5. **Start the Email Agent:**
   ```bash
   python run.py
   ```

### Option 2: Manual PostgreSQL Installation

## 📦 Installing PostgreSQL

### Windows
1. Download PostgreSQL from https://www.postgresql.org/download/windows/
2. Run the installer and follow the setup wizard
3. Remember the password you set for the `postgres` user
4. Default port is 5432

### macOS
```bash
# Using Homebrew
brew install postgresql
brew services start postgresql

# Create a database user
createuser -s email_agent
```

### Ubuntu/Debian
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

### CentOS/RHEL
```bash
sudo yum install postgresql-server postgresql-contrib
sudo postgresql-setup initdb
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

## 🔧 Database Setup

### 1. Create Database and User

Connect to PostgreSQL as superuser:
```bash
sudo -u postgres psql
```

Create database and user:
```sql
-- Create user
CREATE USER email_agent WITH PASSWORD 'secure_password_123';

-- Create database
CREATE DATABASE email_monitor OWNER email_agent;

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE email_monitor TO email_agent;

-- Exit
\q
```

### 2. Configure PostgreSQL (Optional)

Edit PostgreSQL configuration for better performance:

**Find config file location:**
```bash
sudo -u postgres psql -c "SHOW config_file;"
```

**Edit postgresql.conf:**
```bash
sudo nano /etc/postgresql/15/main/postgresql.conf
```

**Recommended settings for Email Agent:**
```ini
# Memory settings
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# Connection settings
max_connections = 100

# Logging
log_statement = 'mod'
log_min_duration_statement = 1000

# Performance
random_page_cost = 1.1
effective_io_concurrency = 200
```

**Edit pg_hba.conf for authentication:**
```bash
sudo nano /etc/postgresql/15/main/pg_hba.conf
```

Add line for local connections:
```
local   email_monitor   email_agent                     md5
host    email_monitor   email_agent     127.0.0.1/32    md5
```

**Restart PostgreSQL:**
```bash
sudo systemctl restart postgresql
```

## 🔧 Environment Configuration

### Update .env file

```env
# Database (PostgreSQL)
DATABASE_URL=postgresql://email_agent:secure_password_123@localhost:5432/email_monitor

# PostgreSQL Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=email_monitor
POSTGRES_USER=email_agent
POSTGRES_PASSWORD=secure_password_123

# Rest of your configuration...
IMAP_HOST=imap.example.com
# ... etc
```

## 🚀 Initialize Database

### Install Python Dependencies

```bash
pip install psycopg2-binary asyncpg
```

### Run Initialization Script

```bash
python scripts/init_postgres.py
```

This script will:
- ✅ Create the database if it doesn't exist
- ✅ Create all required tables
- ✅ Set up indexes for performance
- ✅ Test the connection

## 🔍 Verify Setup

### Test Database Connection

```bash
# Using psql
psql -h localhost -U email_agent -d email_monitor

# List tables
\dt

# Check table structure
\d email_logs
\d whatsapp_notifications
\d email_replies

# Exit
\q
```

### Test with Python

```python
from app.models import get_db, EmailLog

# Test connection
db = get_db()
count = db.query(EmailLog).count()
print(f"Email count: {count}")
db.close()
```

## 📊 Database Management

### Using pgAdmin (Web Interface)

If you used Docker Compose, pgAdmin is available at:
- URL: http://localhost:8080
- Email: <EMAIL>
- Password: admin123

### Useful PostgreSQL Commands

```sql
-- Check database size
SELECT pg_size_pretty(pg_database_size('email_monitor'));

-- Check table sizes
SELECT 
    schemaname,
    tablename,
    pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
FROM pg_tables 
WHERE schemaname = 'public';

-- Get statistics
SELECT * FROM get_email_monitor_stats();

-- Clean old data (older than 90 days)
SELECT cleanup_old_email_data(90);
```

## 🔧 Performance Optimization

### Indexes

The initialization script creates these indexes automatically:
- `email_logs`: message_id, sender, status, received_at
- `whatsapp_notifications`: email_log_id, recipient, status, sent_at
- `email_replies`: email_log_id, reply_to, status, sent_at

### Connection Pooling

The application uses SQLAlchemy connection pooling:
- Pool size: 10 connections
- Max overflow: 20 connections
- Pool recycle: 300 seconds

## 🚨 Troubleshooting

### Common Issues

1. **Connection refused:**
   ```bash
   # Check if PostgreSQL is running
   sudo systemctl status postgresql
   
   # Check if port is open
   netstat -an | grep 5432
   ```

2. **Authentication failed:**
   ```bash
   # Check pg_hba.conf settings
   sudo nano /etc/postgresql/15/main/pg_hba.conf
   
   # Restart PostgreSQL after changes
   sudo systemctl restart postgresql
   ```

3. **Database doesn't exist:**
   ```bash
   # Run the initialization script
   python scripts/init_postgres.py
   ```

4. **Permission denied:**
   ```sql
   -- Grant permissions to user
   GRANT ALL PRIVILEGES ON DATABASE email_monitor TO email_agent;
   GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO email_agent;
   ```

### Logs

Check PostgreSQL logs:
```bash
# Ubuntu/Debian
sudo tail -f /var/log/postgresql/postgresql-15-main.log

# CentOS/RHEL
sudo tail -f /var/lib/pgsql/15/data/log/postgresql-*.log
```

## 🔄 Migration from SQLite

If you have existing SQLite data, you can migrate it:

1. **Export SQLite data:**
   ```bash
   sqlite3 email_monitor.db .dump > sqlite_dump.sql
   ```

2. **Convert and import to PostgreSQL:**
   ```bash
   # Clean up the dump file for PostgreSQL compatibility
   sed 's/INTEGER PRIMARY KEY AUTOINCREMENT/SERIAL PRIMARY KEY/g' sqlite_dump.sql > postgres_dump.sql
   
   # Import to PostgreSQL
   psql -h localhost -U email_agent -d email_monitor -f postgres_dump.sql
   ```

## 🎯 Next Steps

After PostgreSQL is set up:

1. **Start the Email Agent:**
   ```bash
   python run.py
   ```

2. **Monitor logs:**
   ```bash
   tail -f logs/email_monitor.log
   ```

3. **Check API:**
   - API Documentation: http://localhost:8000/docs
   - Database stats: http://localhost:8000/api/stats

Your Email Monitor Agent is now running with PostgreSQL! 🎉
