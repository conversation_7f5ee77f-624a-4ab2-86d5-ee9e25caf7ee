import os
from typing import List, Dict, Optional
from datetime import datetime, timezone, timedelta

from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Float, create_engine
from sqlalchemy.orm import declarative_base, relationship, sessionmaker

# Create SQLAlchemy Base
Base = declarative_base()

class EmailLog(Base):
    """Model for storing email processing logs"""

    __tablename__ = 'email_logs'

    id = Column(Integer, primary_key=True)
    message_id = Column(String(255), unique=True, nullable=False)
    sender = Column(String(255), nullable=False)
    recipient = Column(String(255), nullable=False)
    subject = Column(String(255), nullable=True)
    received_at = Column(DateTime, default=lambda: datetime.now(timezone.utc))
    processed_at = Column(DateTime, nullable=True)
    summary = Column(Text, nullable=True)
    extracted_data = Column(Text, nullable=True)
    whatsapp_summary = Column(Text, nullable=True)
    auto_reply_text = Column(Text, nullable=True)
    status = Column(String(50), default='received')  # received, processed, failed
    error_message = Column(Text, nullable=True)

    # Relationships
    notifications = relationship("WhatsAppNotification", back_populates="email_log")
    sms_notifications = relationship("SMSNotification", back_populates="email_log")
    replies = relationship("EmailReply", back_populates="email_log")
    voice_calls = relationship("VoiceCall", back_populates="email_log")
    voice_call_logs = relationship("VoiceCallLog", back_populates="email_log")
    action_monitors = relationship("ActionMonitor", back_populates="email_log")

    def to_dict(self):
        """Convert model to dictionary for API responses"""
        return {
            'id': self.id,
            'message_id': self.message_id,
            'sender': self.sender,
            'recipient': self.recipient,
            'subject': self.subject,
            'received_at': self.received_at.isoformat() if self.received_at else None,
            'processed_at': self.processed_at.isoformat() if self.processed_at else None,
            'summary': self.summary,
            'extracted_data': self.extracted_data,
            'whatsapp_summary': self.whatsapp_summary,
            'auto_reply_text': self.auto_reply_text,
            'status': self.status,
            'error_message': self.error_message
        }


class WhatsAppNotification(Base):
    """Model for storing WhatsApp notification logs"""

    __tablename__ = 'whatsapp_notifications'

    id = Column(Integer, primary_key=True)
    email_log_id = Column(Integer, ForeignKey('email_logs.id'), nullable=False)
    recipient = Column(String(50), nullable=False)  # Phone number
    message = Column(Text, nullable=False)
    sent_at = Column(DateTime, nullable=True)
    status = Column(String(50), default='pending')  # pending, sent, delivered, failed
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0)

    # Relationship
    email_log = relationship("EmailLog", back_populates="notifications")

    def to_dict(self):
        """Convert model to dictionary for API responses"""
        return {
            'id': self.id,
            'email_log_id': self.email_log_id,
            'recipient': self.recipient,
            'message': self.message,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None,
            'status': self.status,
            'error_message': self.error_message,
            'retry_count': self.retry_count
        }


class SMSNotification(Base):
    """Model for storing SMS notification logs"""

    __tablename__ = 'sms_notifications'

    id = Column(Integer, primary_key=True)
    email_log_id = Column(Integer, ForeignKey('email_logs.id'), nullable=False)
    recipient = Column(String(50), nullable=False)  # Phone number
    employee_name = Column(String(255), nullable=True)  # Employee name for personalization
    message = Column(Text, nullable=False)
    sent_at = Column(DateTime, nullable=True)
    status = Column(String(50), default='pending')  # pending, sent, delivered, failed
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0)
    message_id = Column(String(255), nullable=True)  # Vonage message ID
    cost = Column(Float, nullable=True)  # SMS cost
    network = Column(String(100), nullable=True)  # Network provider

    # Relationship
    email_log = relationship("EmailLog", back_populates="sms_notifications")

    def to_dict(self):
        """Convert model to dictionary for API responses"""
        return {
            'id': self.id,
            'email_log_id': self.email_log_id,
            'recipient': self.recipient,
            'employee_name': self.employee_name,
            'message': self.message,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None,
            'status': self.status,
            'error_message': self.error_message,
            'retry_count': self.retry_count,
            'message_id': self.message_id,
            'cost': self.cost,
            'network': self.network
        }


class EmailReply(Base):
    """Model for storing email reply logs"""

    __tablename__ = 'email_replies'

    id = Column(Integer, primary_key=True)
    email_log_id = Column(Integer, ForeignKey('email_logs.id'), nullable=False)
    reply_to = Column(String(255), nullable=False)
    subject = Column(String(255), nullable=True)
    body = Column(Text, nullable=False)
    sent_at = Column(DateTime, nullable=True)
    status = Column(String(50), default='pending')  # pending, sent, failed
    error_message = Column(Text, nullable=True)

    # Relationship
    email_log = relationship("EmailLog", back_populates="replies")

    def to_dict(self):
        """Convert model to dictionary for API responses"""
        return {
            'id': self.id,
            'email_log_id': self.email_log_id,
            'reply_to': self.reply_to,
            'subject': self.subject,
            'body': self.body,
            'sent_at': self.sent_at.isoformat() if self.sent_at else None,
            'status': self.status,
            'error_message': self.error_message
        }


class VoiceCall(Base):
    """Model for storing voice call logs via VAPI"""

    __tablename__ = 'voice_calls'

    id = Column(Integer, primary_key=True)
    email_log_id = Column(Integer, ForeignKey('email_logs.id'), nullable=False)
    recipient = Column(String(50), nullable=False)  # Phone number
    employee_name = Column(String(255), nullable=True)  # Employee name for personalization
    vapi_call_id = Column(String(255), nullable=True)  # VAPI call ID
    status = Column(String(50), default='pending')  # pending, initiated, ringing, answered, completed, failed, no_answer
    initiated_at = Column(DateTime, nullable=True)
    answered_at = Column(DateTime, nullable=True)
    ended_at = Column(DateTime, nullable=True)
    duration = Column(Integer, nullable=True)  # Call duration in seconds
    cost = Column(Float, nullable=True)  # Call cost
    ended_reason = Column(String(100), nullable=True)  # Reason call ended
    error_message = Column(Text, nullable=True)
    retry_count = Column(Integer, default=0)
    next_retry_at = Column(DateTime, nullable=True)  # When to retry if call failed

    # Relationship
    email_log = relationship("EmailLog", back_populates="voice_calls")

    def to_dict(self):
        """Convert model to dictionary for API responses"""
        return {
            'id': self.id,
            'email_log_id': self.email_log_id,
            'recipient': self.recipient,
            'employee_name': self.employee_name,
            'vapi_call_id': self.vapi_call_id,
            'status': self.status,
            'initiated_at': self.initiated_at.isoformat() if self.initiated_at else None,
            'answered_at': self.answered_at.isoformat() if self.answered_at else None,
            'ended_at': self.ended_at.isoformat() if self.ended_at else None,
            'duration': self.duration,
            'cost': self.cost,
            'ended_reason': self.ended_reason,
            'error_message': self.error_message,
            'retry_count': self.retry_count,
            'next_retry_at': self.next_retry_at.isoformat() if self.next_retry_at else None
        }


class ActionMonitor(Base):
    """Model for monitoring all system actions and events"""

    __tablename__ = 'action_monitors'

    id = Column(Integer, primary_key=True)
    email_log_id = Column(Integer, ForeignKey('email_logs.id'), nullable=True)  # Optional link to email
    action_type = Column(String(50), nullable=False)  # email_received, whatsapp_sent, voice_call, email_reply, system_event
    action_category = Column(String(50), nullable=False)  # notification, communication, processing, system
    action_description = Column(Text, nullable=False)  # Human-readable description
    target_recipient = Column(String(255), nullable=True)  # Phone/email of recipient
    employee_name = Column(String(255), nullable=True)  # Employee name if applicable
    status = Column(String(50), nullable=False)  # success, failed, pending, in_progress

    # Timing information
    initiated_at = Column(DateTime, nullable=False, default=lambda: datetime.now(timezone.utc))
    completed_at = Column(DateTime, nullable=True)
    duration_seconds = Column(Integer, nullable=True)  # How long the action took

    # Result information
    success = Column(String(10), nullable=False, default='pending')  # true, false, pending
    error_message = Column(Text, nullable=True)
    response_data = Column(Text, nullable=True)  # JSON response from APIs

    # Cost and metrics
    cost = Column(Float, nullable=True)  # Cost of the action (for calls, SMS, etc.)
    retry_count = Column(Integer, default=0)
    next_retry_at = Column(DateTime, nullable=True)

    # Metadata
    user_agent = Column(String(255), nullable=True)  # For web actions
    ip_address = Column(String(45), nullable=True)  # For web actions
    additional_data = Column(Text, nullable=True)  # JSON for extra information

    # Relationships
    email_log = relationship("EmailLog", back_populates="action_monitors")

    def to_dict(self):
        """Convert model to dictionary for API responses"""
        return {
            'id': self.id,
            'email_log_id': self.email_log_id,
            'action_type': self.action_type,
            'action_category': self.action_category,
            'action_description': self.action_description,
            'target_recipient': self.target_recipient,
            'employee_name': self.employee_name,
            'status': self.status,
            'initiated_at': self.initiated_at.isoformat() if self.initiated_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'duration_seconds': self.duration_seconds,
            'success': self.success,
            'error_message': self.error_message,
            'response_data': self.response_data,
            'cost': self.cost,
            'retry_count': self.retry_count,
            'next_retry_at': self.next_retry_at.isoformat() if self.next_retry_at else None,
            'user_agent': self.user_agent,
            'ip_address': self.ip_address,
            'additional_data': self.additional_data
        }

    def mark_completed(self, success: bool, error_message: str = None, response_data: str = None, cost: float = None):
        """Mark the action as completed"""
        self.completed_at = datetime.now(timezone.utc)
        self.success = 'true' if success else 'false'
        self.status = 'success' if success else 'failed'

        if self.initiated_at:
            duration = self.completed_at - self.initiated_at
            self.duration_seconds = int(duration.total_seconds())

        if error_message:
            self.error_message = error_message
        if response_data:
            self.response_data = response_data
        if cost is not None:
            self.cost = cost

    def schedule_retry(self, delay_minutes: int = 10):
        """Schedule this action for retry"""
        self.retry_count += 1
        self.next_retry_at = datetime.now(timezone.utc) + timedelta(minutes=delay_minutes)
        self.status = 'pending_retry'


class VoiceCallLog(Base):
    """Enhanced model for tracking detailed voice call analytics and metrics"""

    __tablename__ = 'voice_call_logs'

    id = Column(Integer, primary_key=True)
    email_log_id = Column(Integer, ForeignKey('email_logs.id'), nullable=True)  # Link to email
    employee_id = Column(Integer, nullable=True)  # Employee ID from API
    employee_name = Column(String(255), nullable=False)  # Employee name
    employee_phone = Column(String(50), nullable=False)  # Phone number called

    # Call Provider Details (Generic)
    call_provider = Column(String(100), nullable=True)  # Call provider name (e.g., Twilio, Vonage)
    external_call_id = Column(String(255), nullable=True)  # External provider's call ID
    provider_config_id = Column(String(255), nullable=True)  # Provider configuration ID

    # Call Status and Timing
    status = Column(String(50), nullable=False, default='pending')  # pending, initiated, ringing, answered, completed, failed, no_answer
    call_direction = Column(String(20), default='outbound')  # outbound, inbound
    initiated_at = Column(DateTime, nullable=False, default=lambda: datetime.now(timezone.utc))
    answered_at = Column(DateTime, nullable=True)  # When call was answered
    ended_at = Column(DateTime, nullable=True)  # When call ended
    duration_seconds = Column(Integer, nullable=True)  # Total call duration
    ring_duration_seconds = Column(Integer, nullable=True)  # Time spent ringing
    talk_duration_seconds = Column(Integer, nullable=True)  # Actual conversation time

    # Call Quality and Analytics
    call_quality_score = Column(Float, nullable=True)  # Quality score from VAPI (0-1)
    audio_quality = Column(String(20), nullable=True)  # excellent, good, fair, poor
    connection_quality = Column(String(20), nullable=True)  # stable, unstable, dropped
    background_noise_level = Column(String(20), nullable=True)  # low, medium, high

    # Cost and Billing
    cost_per_minute = Column(Float, nullable=True)  # Cost per minute rate
    total_cost = Column(Float, nullable=True)  # Total cost of the call
    currency = Column(String(10), default='USD')  # Currency for cost
    billing_increment = Column(Integer, default=60)  # Billing increment in seconds

    # Call Content and Context
    call_purpose = Column(String(100), nullable=True)  # email_notification, follow_up, urgent_alert
    message_template = Column(Text, nullable=True)  # Template used for the call
    actual_message = Column(Text, nullable=True)  # Actual message delivered
    call_transcript = Column(Text, nullable=True)  # Call transcript if available

    # Response and Interaction
    call_answered = Column(String(10), default='false')  # true/false
    human_detected = Column(String(10), default='false')  # true/false (vs voicemail)
    interaction_type = Column(String(50), nullable=True)  # human, voicemail, busy, no_answer
    customer_response = Column(Text, nullable=True)  # Any response from the recipient
    call_rating = Column(Integer, nullable=True)  # 1-5 rating if feedback provided

    # Technical Details
    caller_id = Column(String(50), nullable=True)  # Caller ID used
    carrier_name = Column(String(100), nullable=True)  # Recipient's carrier
    country_code = Column(String(10), nullable=True)  # Country code of recipient
    timezone = Column(String(50), nullable=True)  # Recipient's timezone
    device_type = Column(String(50), nullable=True)  # mobile, landline, voip

    # Retry and Error Handling
    retry_count = Column(Integer, default=0)  # Number of retry attempts
    max_retries = Column(Integer, default=3)  # Maximum retries allowed
    next_retry_at = Column(DateTime, nullable=True)  # When to retry next
    retry_reason = Column(String(255), nullable=True)  # Reason for retry
    final_status = Column(String(50), nullable=True)  # Final status after all retries

    # Error and Failure Details
    error_code = Column(String(50), nullable=True)  # Error code from VAPI
    error_message = Column(Text, nullable=True)  # Detailed error message
    failure_reason = Column(String(255), nullable=True)  # Human-readable failure reason
    network_error = Column(String(10), default='false')  # true/false

    # Compliance and Legal
    consent_status = Column(String(50), default='assumed')  # explicit, assumed, unknown
    recording_enabled = Column(String(10), default='false')  # true/false
    recording_url = Column(String(500), nullable=True)  # URL to call recording
    data_retention_days = Column(Integer, default=90)  # How long to keep data

    # Metadata and Tracking
    user_agent = Column(String(255), nullable=True)  # System that initiated call
    ip_address = Column(String(45), nullable=True)  # IP address of initiator
    session_id = Column(String(255), nullable=True)  # Session ID for tracking
    correlation_id = Column(String(255), nullable=True)  # For correlating related calls
    tags = Column(Text, nullable=True)  # JSON array of tags for categorization

    # Relationships
    email_log = relationship("EmailLog", back_populates="voice_call_logs")

    def to_dict(self):
        """Convert model to dictionary for API responses"""
        return {
            'id': self.id,
            'email_log_id': self.email_log_id,
            'employee_id': self.employee_id,
            'employee_name': self.employee_name,
            'employee_phone': self.employee_phone,
            'call_provider': self.call_provider,
            'external_call_id': self.external_call_id,
            'provider_config_id': self.provider_config_id,
            'status': self.status,
            'call_direction': self.call_direction,
            'initiated_at': self.initiated_at.isoformat() if self.initiated_at else None,
            'answered_at': self.answered_at.isoformat() if self.answered_at else None,
            'ended_at': self.ended_at.isoformat() if self.ended_at else None,
            'duration_seconds': self.duration_seconds,
            'ring_duration_seconds': self.ring_duration_seconds,
            'talk_duration_seconds': self.talk_duration_seconds,
            'call_quality_score': self.call_quality_score,
            'audio_quality': self.audio_quality,
            'connection_quality': self.connection_quality,
            'background_noise_level': self.background_noise_level,
            'cost_per_minute': self.cost_per_minute,
            'total_cost': self.total_cost,
            'currency': self.currency,
            'billing_increment': self.billing_increment,
            'call_purpose': self.call_purpose,
            'message_template': self.message_template,
            'actual_message': self.actual_message,
            'call_transcript': self.call_transcript,
            'call_answered': self.call_answered,
            'human_detected': self.human_detected,
            'interaction_type': self.interaction_type,
            'customer_response': self.customer_response,
            'call_rating': self.call_rating,
            'caller_id': self.caller_id,
            'carrier_name': self.carrier_name,
            'country_code': self.country_code,
            'timezone': self.timezone,
            'device_type': self.device_type,
            'retry_count': self.retry_count,
            'max_retries': self.max_retries,
            'next_retry_at': self.next_retry_at.isoformat() if self.next_retry_at else None,
            'retry_reason': self.retry_reason,
            'final_status': self.final_status,
            'error_code': self.error_code,
            'error_message': self.error_message,
            'failure_reason': self.failure_reason,
            'network_error': self.network_error,
            'consent_status': self.consent_status,
            'recording_enabled': self.recording_enabled,
            'recording_url': self.recording_url,
            'data_retention_days': self.data_retention_days,
            'user_agent': self.user_agent,
            'ip_address': self.ip_address,
            'session_id': self.session_id,
            'correlation_id': self.correlation_id,
            'tags': self.tags
        }

    def mark_answered(self):
        """Mark the call as answered"""
        self.answered_at = datetime.now(timezone.utc)
        self.status = 'answered'
        self.call_answered = 'true'
        if self.initiated_at:
            ring_duration = self.answered_at - self.initiated_at
            self.ring_duration_seconds = int(ring_duration.total_seconds())

    def mark_completed(self, success: bool = True, error_message: str = None, cost: float = None):
        """Mark the call as completed"""
        self.ended_at = datetime.now(timezone.utc)
        self.status = 'completed' if success else 'failed'

        if self.answered_at:
            talk_duration = self.ended_at - self.answered_at
            self.talk_duration_seconds = int(talk_duration.total_seconds())

        if self.initiated_at:
            total_duration = self.ended_at - self.initiated_at
            self.duration_seconds = int(total_duration.total_seconds())

        if error_message:
            self.error_message = error_message
            self.failure_reason = error_message

        if cost is not None:
            self.total_cost = cost

    def schedule_retry(self, delay_minutes: int = 10, reason: str = None):
        """Schedule this call for retry"""
        self.retry_count += 1
        self.next_retry_at = datetime.now(timezone.utc) + timedelta(minutes=delay_minutes)
        self.retry_reason = reason
        self.status = 'pending_retry'

        if self.retry_count >= self.max_retries:
            self.final_status = 'failed_permanently'
            self.status = 'failed_permanently'

    def calculate_cost(self):
        """Calculate call cost based on duration and rate"""
        if self.duration_seconds and self.cost_per_minute:
            # Round up to billing increment
            billable_seconds = ((self.duration_seconds + self.billing_increment - 1) // self.billing_increment) * self.billing_increment
            billable_minutes = billable_seconds / 60.0
            self.total_cost = round(billable_minutes * self.cost_per_minute, 4)
        return self.total_cost

    def get_call_summary(self):
        """Get a summary of the call for reporting"""
        return {
            'employee': self.employee_name,
            'phone': self.employee_phone,
            'status': self.status,
            'duration': f"{self.duration_seconds}s" if self.duration_seconds else "0s",
            'cost': f"${self.total_cost:.4f}" if self.total_cost else "$0.00",
            'answered': self.call_answered == 'true',
            'quality': self.audio_quality or 'unknown',
            'retries': self.retry_count
        }


# Database setup function
def get_db():
    """
    Create database engine and session for PostgreSQL
    """
    SQLALCHEMY_DATABASE_URL = os.getenv(
        "DATABASE_URL",
        "postgresql://username:password@localhost:5432/email_monitor"
    )

    # PostgreSQL engine configuration
    engine = create_engine(
        SQLALCHEMY_DATABASE_URL,
        pool_size=10,
        max_overflow=20,
        pool_pre_ping=True,
        pool_recycle=300
    )

    # Create tables
    Base.metadata.create_all(bind=engine)

    # Create session
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    return SessionLocal()
