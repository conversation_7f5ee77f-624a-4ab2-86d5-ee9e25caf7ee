"""
Vonage Voice API integration for making voice calls to employees.
"""

import json
import logging
import requests
from typing import Dict, Optional
from datetime import datetime, timezone
from ..models import EmailLog

logger = logging.getLogger(__name__)


class VonageCaller:
    """
    Vonage Voice API integration for making voice calls to employees.
    """
    
    def __init__(self, 
                 api_key: str,
                 api_secret: str,
                 application_id: str,
                 private_key: str = None,
                 from_number: str = None):
        """
        Initialize the Vonage caller.
        
        Args:
            api_key: Vonage API key
            api_secret: Vonage API secret
            application_id: Vonage application ID for voice calls
            private_key: Private key for JWT authentication (optional)
            from_number: Phone number to call from (optional)
        """
        self.api_key = api_key
        self.api_secret = api_secret
        self.application_id = application_id
        self.private_key = private_key
        self.from_number = from_number or "Unknown"
        self.base_url = "https://api.nexmo.com/v1/calls"
        
    def make_call(self, 
                  recipient_phone: str, 
                  email_log: Email<PERSON>og, 
                  employee_name: str = None) -> Dict:
        """
        Make a voice call to an employee using Vonage Voice API.
        
        Args:
            recipient_phone: Phone number to call (with country code)
            email_log: EmailLog instance containing email details
            employee_name: Name of the employee being called
            
        Returns:
            Dict: Call response with call_id and status
        """
        try:
            # Prepare call message
            call_message = self._format_call_message(email_log, employee_name)
            
            # Vonage Voice API payload
            payload = {
                "to": [{
                    "type": "phone",
                    "number": recipient_phone
                }],
                "from": {
                    "type": "phone", 
                    "number": self.from_number
                },
                "ncco": [
                    {
                        "action": "talk",
                        "text": call_message,
                        "voiceName": "Amy",
                        "language": "en-US"
                    }
                ]
            }
            
            # Make the API call
            headers = {
                "Authorization": f"Bearer {self._get_jwt_token()}",
                "Content-Type": "application/json"
            }
            
            logger.info(f"Making Vonage voice call to {recipient_phone} for employee {employee_name}")
            
            response = requests.post(
                self.base_url,
                headers=headers,
                json=payload,
                timeout=30
            )
            
            if response.status_code == 201:
                call_data = response.json()
                logger.info(f"Vonage call initiated successfully: {call_data.get('uuid')}")
                return {
                    'success': True,
                    'call_id': call_data.get('uuid'),
                    'status': call_data.get('status', 'initiated'),
                    'direction': call_data.get('direction', 'outbound'),
                    'to': call_data.get('to'),
                    'from': call_data.get('from'),
                    'conversation_uuid': call_data.get('conversation_uuid')
                }
            else:
                error_msg = f"Vonage API error: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {
                    'success': False,
                    'error': error_msg,
                    'status_code': response.status_code
                }
                
        except requests.exceptions.Timeout:
            error_msg = "Vonage API call timed out"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}
            
        except requests.exceptions.RequestException as e:
            error_msg = f"Vonage API request failed: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}
            
        except Exception as e:
            error_msg = f"Unexpected error during Vonage call: {str(e)}"
            logger.error(error_msg)
            return {'success': False, 'error': error_msg}

    def _format_call_message(self, email_log: EmailLog, employee_name: str = None) -> str:
        """
        Format the voice message for the call.
        
        Args:
            email_log: EmailLog instance
            employee_name: Employee name for personalization
            
        Returns:
            str: Formatted voice message
        """
        # Extract sender name (remove email if present)
        sender_name = email_log.sender.split('<')[0].strip() if '<' in email_log.sender else email_log.sender
        
        # Create personalized message
        greeting = f"Hello {employee_name}, " if employee_name else "Hello, "
        
        message = (
            f"{greeting}this is an automated notification from the Email Monitor Agent. "
            f"You have received an important email from {sender_name}. "
        )
        
        if email_log.subject:
            message += f"The subject is: {email_log.subject}. "
            
        if email_log.whatsapp_summary:
            # Limit summary length for voice
            summary = email_log.whatsapp_summary[:200] + "..." if len(email_log.whatsapp_summary) > 200 else email_log.whatsapp_summary
            message += f"Summary: {summary}. "
            
        message += "Please check your email for full details. Thank you."
        
        return message

    def _get_jwt_token(self) -> str:
        """
        Generate JWT token for Vonage API authentication.
        
        Returns:
            str: JWT token
        """
        # For now, use basic auth with API key/secret
        # In production, implement proper JWT token generation
        import base64
        credentials = f"{self.api_key}:{self.api_secret}"
        encoded_credentials = base64.b64encode(credentials.encode()).decode()
        return encoded_credentials

    def get_call_status(self, call_id: str) -> Dict:
        """
        Get the status of a specific call.
        
        Args:
            call_id: Vonage call UUID
            
        Returns:
            Dict: Call status information
        """
        try:
            headers = {
                "Authorization": f"Bearer {self._get_jwt_token()}",
                "Content-Type": "application/json"
            }
            
            response = requests.get(
                f"{self.base_url}/{call_id}",
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                return {
                    'success': True,
                    'call_data': response.json()
                }
            else:
                return {
                    'success': False,
                    'error': f"Failed to get call status: {response.status_code}"
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"Error getting call status: {str(e)}"
            }


def get_vonage_caller(config: Dict) -> VonageCaller:
    """
    Factory function to get the Vonage caller based on configuration.
    
    Args:
        config: Configuration dictionary containing:
            - vonage_api_key: Vonage API key
            - vonage_api_secret: Vonage API secret
            - vonage_application_id: Vonage application ID
            - vonage_private_key: (optional) Private key for JWT
            - vonage_from_number: (optional) From phone number
        
    Returns:
        VonageCaller: Vonage caller instance
    """
    return VonageCaller(
        api_key=config['vonage_api_key'],
        api_secret=config['vonage_api_secret'],
        application_id=config['vonage_application_id'],
        private_key=config.get('vonage_private_key'),
        from_number=config.get('vonage_from_number')
    )
