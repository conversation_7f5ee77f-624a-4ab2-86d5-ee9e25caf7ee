# Voice Call Analytics Database Table

This document describes the comprehensive `voice_call_logs` table for tracking detailed voice call analytics and metrics in the Email Monitor Agent system.

## 🎯 **Overview**

The `VoiceCallLog` table provides advanced analytics and tracking capabilities for voice calls made through various voice calling providers. It captures detailed metrics including call quality, costs, timing, and interaction data.

## 📊 **Table Schema**

### **Basic Information**
| Field | Type | Description |
|-------|------|-------------|
| `id` | Integer | Primary key |
| `email_log_id` | Integer | Link to associated email (nullable) |
| `employee_id` | Integer | Employee ID from API (nullable) |
| `employee_name` | String(255) | Employee name |
| `employee_phone` | String(50) | Phone number called |

### **Call Provider Details**
| Field | Type | Description |
|-------|------|-------------|
| `call_provider` | String(100) | Call provider name (e.g., <PERSON><PERSON><PERSON>, <PERSON><PERSON>) |
| `external_call_id` | String(255) | External provider's unique call ID |
| `provider_config_id` | String(255) | Provider configuration ID |

### **Call Status and Timing**
| Field | Type | Description |
|-------|------|-------------|
| `status` | String(50) | Call status (pending, initiated, ringing, answered, completed, failed, no_answer) |
| `call_direction` | String(20) | Call direction (outbound, inbound) |
| `initiated_at` | DateTime | When call was initiated |
| `answered_at` | DateTime | When call was answered |
| `ended_at` | DateTime | When call ended |
| `duration_seconds` | Integer | Total call duration |
| `ring_duration_seconds` | Integer | Time spent ringing |
| `talk_duration_seconds` | Integer | Actual conversation time |

### **Call Quality and Analytics**
| Field | Type | Description |
|-------|------|-------------|
| `call_quality_score` | Float | Quality score from VAPI (0-1) |
| `audio_quality` | String(20) | Audio quality (excellent, good, fair, poor) |
| `connection_quality` | String(20) | Connection stability (stable, unstable, dropped) |
| `background_noise_level` | String(20) | Noise level (low, medium, high) |

### **Cost and Billing**
| Field | Type | Description |
|-------|------|-------------|
| `cost_per_minute` | Float | Rate per minute |
| `total_cost` | Float | Total cost of the call |
| `currency` | String(10) | Currency (default: USD) |
| `billing_increment` | Integer | Billing increment in seconds (default: 60) |

### **Call Content and Context**
| Field | Type | Description |
|-------|------|-------------|
| `call_purpose` | String(100) | Purpose (email_notification, follow_up, urgent_alert) |
| `message_template` | Text | Template used for the call |
| `actual_message` | Text | Actual message delivered |
| `call_transcript` | Text | Call transcript if available |

### **Response and Interaction**
| Field | Type | Description |
|-------|------|-------------|
| `call_answered` | String(10) | Whether call was answered (true/false) |
| `human_detected` | String(10) | Human vs voicemail detection (true/false) |
| `interaction_type` | String(50) | Type (human, voicemail, busy, no_answer) |
| `customer_response` | Text | Any response from recipient |
| `call_rating` | Integer | 1-5 rating if feedback provided |

### **Technical Details**
| Field | Type | Description |
|-------|------|-------------|
| `caller_id` | String(50) | Caller ID used |
| `carrier_name` | String(100) | Recipient's carrier |
| `country_code` | String(10) | Country code of recipient |
| `timezone` | String(50) | Recipient's timezone |
| `device_type` | String(50) | Device type (mobile, landline, voip) |

### **Retry and Error Handling**
| Field | Type | Description |
|-------|------|-------------|
| `retry_count` | Integer | Number of retry attempts |
| `max_retries` | Integer | Maximum retries allowed (default: 3) |
| `next_retry_at` | DateTime | When to retry next |
| `retry_reason` | String(255) | Reason for retry |
| `final_status` | String(50) | Final status after all retries |

### **Error and Failure Details**
| Field | Type | Description |
|-------|------|-------------|
| `error_code` | String(50) | Error code from VAPI |
| `error_message` | Text | Detailed error message |
| `failure_reason` | String(255) | Human-readable failure reason |
| `network_error` | String(10) | Network error flag (true/false) |

### **Compliance and Legal**
| Field | Type | Description |
|-------|------|-------------|
| `consent_status` | String(50) | Consent status (explicit, assumed, unknown) |
| `recording_enabled` | String(10) | Recording enabled flag (true/false) |
| `recording_url` | String(500) | URL to call recording |
| `data_retention_days` | Integer | Data retention period (default: 90) |

### **Metadata and Tracking**
| Field | Type | Description |
|-------|------|-------------|
| `user_agent` | String(255) | System that initiated call |
| `ip_address` | String(45) | IP address of initiator |
| `session_id` | String(255) | Session ID for tracking |
| `correlation_id` | String(255) | For correlating related calls |
| `tags` | Text | JSON array of tags for categorization |

## 🔧 **Model Methods**

### **mark_answered()**
Marks the call as answered and calculates ring duration.

### **mark_completed(success, error_message, cost)**
Marks the call as completed with final metrics and calculates talk duration.

### **schedule_retry(delay_minutes, reason)**
Schedules the call for retry with specified delay and reason.

### **calculate_cost()**
Calculates call cost based on duration and rate, rounded to billing increment.

### **get_call_summary()**
Returns a summary dictionary with key metrics for reporting.

## 🚀 **API Endpoints**

### **Get Call Analytics**
```http
GET /api/voice-calls/analytics?hours=24
```
Returns comprehensive analytics including success rates, costs, and quality metrics.

### **Get Employee Call Statistics**
```http
GET /api/voice-calls/employee-stats?employee_id=123&hours=24
```
Returns call statistics grouped by employee.

### **Get Call Logs**
```http
GET /api/voice-calls/logs?status=completed&limit=50&offset=0
```
Returns filtered call logs with pagination.

### **Get Call Details**
```http
GET /api/voice-calls/{call_id}
```
Returns detailed information for a specific call.

### **Update Call Status**
```http
POST /api/voice-calls/{call_id}/update-status
```
Updates call status and metrics.

## 📈 **Analytics Capabilities**

### **Call Performance Metrics**
- **Answer Rate**: Percentage of calls answered
- **Completion Rate**: Percentage of calls completed successfully
- **Average Duration**: Mean call duration
- **Average Talk Time**: Mean conversation time
- **Cost Per Call**: Average cost per call

### **Quality Analytics**
- **Audio Quality Distribution**: Breakdown by quality levels
- **Connection Quality**: Stability metrics
- **Background Noise Analysis**: Noise level distribution
- **Human Detection Rate**: Human vs voicemail detection

### **Employee Performance**
- **Calls per Employee**: Volume by employee
- **Employee Answer Rates**: Individual performance
- **Cost by Employee**: Spending analysis
- **Response Quality**: Employee-specific metrics

### **Time-based Analysis**
- **Hourly Call Volume**: Peak calling times
- **Daily Trends**: Call patterns over time
- **Retry Analysis**: Failure and retry patterns
- **Cost Trends**: Spending over time

## 🔍 **Sample Queries**

### **Get Call Success Rate**
```sql
SELECT
    COUNT(*) as total_calls,
    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as successful_calls,
    ROUND(SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) * 100.0 / COUNT(*), 2) as success_rate
FROM voice_call_logs
WHERE initiated_at >= NOW() - INTERVAL 24 HOUR;
```

### **Get Average Call Cost by Employee**
```sql
SELECT
    employee_name,
    COUNT(*) as total_calls,
    SUM(total_cost) as total_cost,
    AVG(total_cost) as avg_cost_per_call
FROM voice_call_logs
WHERE total_cost IS NOT NULL
GROUP BY employee_name
ORDER BY total_cost DESC;
```

### **Get Quality Distribution**
```sql
SELECT
    audio_quality,
    COUNT(*) as count,
    ROUND(COUNT(*) * 100.0 / (SELECT COUNT(*) FROM voice_call_logs), 2) as percentage
FROM voice_call_logs
WHERE audio_quality IS NOT NULL
GROUP BY audio_quality;
```

### **Get Failed Calls Needing Retry**
```sql
SELECT * FROM voice_call_logs
WHERE status IN ('failed', 'no_answer')
AND retry_count < max_retries
AND (next_retry_at IS NULL OR next_retry_at <= NOW());
```

## 🛠️ **Integration with Existing System**

### **Relationship with EmailLog**
- Links to `email_logs` table via `email_log_id`
- Enables tracking call chains for specific emails
- Supports email-to-call analytics

### **Action Monitoring Integration**
- Integrates with action monitoring system
- Provides detailed call tracking
- Supports comprehensive audit trails

### **Voice Provider Integration**
- Stores provider-specific identifiers
- Tracks call quality metrics from various providers
- Supports webhook updates from voice providers

## 💡 **Best Practices**

### **Data Management**
- **Regular Cleanup**: Archive old call logs based on retention policy
- **Index Optimization**: Add indexes on frequently queried columns
- **Cost Monitoring**: Set up alerts for cost thresholds
- **Quality Tracking**: Monitor quality trends for service optimization

### **Analytics Usage**
- **Daily Reviews**: Check success rates and failed calls
- **Weekly Reports**: Analyze employee performance and costs
- **Monthly Analysis**: Review trends and optimization opportunities
- **Quality Monitoring**: Track audio and connection quality metrics

The `VoiceCallLog` table provides comprehensive analytics capabilities for optimizing voice calling performance, managing costs, and ensuring high-quality customer interactions.
