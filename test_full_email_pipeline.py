#!/usr/bin/env python3
"""
Test the full email processing pipeline including SMS notifications
"""

import sys
import os
import time
sys.path.append('.')

from app.models import EmailLog, get_db
from app.worker import get_config
from app.services.notification_service import get_notification_service
from app.services.ai_summarizer import get_ai_summarizer

def create_test_email():
    """Create a test email in the database"""
    print("📧 Creating test email...")
    
    db = get_db()
    
    # Create a test email
    test_email = EmailLog(
        message_id=f"test-email-{int(time.time())}",
        sender="<EMAIL>",
        recipient="<EMAIL>",
        subject="Test Email for SMS Notification",
        status="received"
    )
    
    db.add(test_email)
    db.commit()
    
    print(f"✅ Created test email with ID: {test_email.id}")
    return test_email, db

def test_ai_processing(email_log, db):
    """Test AI processing to generate WhatsApp summary"""
    print("\n🤖 Testing AI processing...")
    
    try:
        config = get_config()
        ai_summarizer = get_ai_summarizer(config)
        
        # Simulate email content
        email_content = """
        Subject: Test Email for SMS Notification
        
        This is a test email to verify that SMS notifications are working properly 
        in the email monitoring system. The system should process this email and 
        send notifications to all active employees via WhatsApp and SMS.
        
        Please confirm receipt of this notification.
        """
        
        # Process with AI
        ai_result = ai_summarizer.process_email(email_log, email_content, db)
        
        if ai_result.get('success'):
            print(f"✅ AI processing completed")
            print(f"   📝 WhatsApp Summary: {email_log.whatsapp_summary[:100]}...")
            return True
        else:
            print(f"❌ AI processing failed: {ai_result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ AI processing error: {str(e)}")
        return False

def test_notification_service(email_log, db):
    """Test the full notification service (WhatsApp + SMS + Voice)"""
    print("\n📱 Testing notification service...")
    
    try:
        config = get_config()
        notification_service = get_notification_service(config)
        
        print(f"SMS Enabled: {config.get('sms_enabled')}")
        print(f"WhatsApp Enabled: {not config.get('mock_whatsapp', True)}")
        print(f"Voice Calling Enabled: {config.get('voice_calling_enabled')}")
        
        # Send all notifications
        results = notification_service.send_notifications(email_log, db)
        
        print(f"\n📊 Notification Results:")
        print(f"   📱 WhatsApp notifications: {len(results.get('whatsapp_notifications', []))}")
        print(f"   📨 SMS notifications: {len(results.get('sms_notifications', []))}")
        print(f"   📞 Voice calls: {len(results.get('voice_calls', []))}")
        print(f"   ✅ Successful: {results.get('success_count', 0)}")
        print(f"   ❌ Failed: {results.get('failure_count', 0)}")
        
        # Show detailed SMS results
        sms_notifications = results.get('sms_notifications', [])
        if sms_notifications:
            print(f"\n📨 SMS Notification Details:")
            for sms in sms_notifications:
                status_icon = "✅" if sms['status'] == 'sent' else "❌"
                print(f"   {status_icon} {sms['employee_name']} ({sms['recipient']})")
                print(f"      Status: {sms['status']}")
                if sms.get('message_id'):
                    print(f"      Message ID: {sms['message_id']}")
                if sms.get('error_message'):
                    print(f"      Error: {sms['error_message']}")
                if sms.get('sent_at'):
                    print(f"      Sent at: {sms['sent_at']}")
        else:
            print(f"\n⚠️  No SMS notifications were sent!")
        
        # Show detailed WhatsApp results
        whatsapp_notifications = results.get('whatsapp_notifications', [])
        if whatsapp_notifications:
            print(f"\n📱 WhatsApp Notification Details:")
            for wa in whatsapp_notifications:
                status_icon = "✅" if wa['status'] == 'sent' else "❌"
                print(f"   {status_icon} {wa['recipient']}")
                print(f"      Status: {wa['status']}")
                if wa.get('message_id'):
                    print(f"      Message ID: {wa['message_id']}")
                if wa.get('error_message'):
                    print(f"      Error: {wa['error_message']}")
        
        return results
        
    except Exception as e:
        print(f"❌ Notification service error: {str(e)}")
        import traceback
        traceback.print_exc()
        return {}

def check_database_records(email_log, db):
    """Check what was actually saved in the database"""
    print("\n🔍 Checking database records...")
    
    try:
        from app.models import WhatsAppNotification, SMSNotification, VoiceCall
        
        # Check WhatsApp notifications
        whatsapp_count = db.query(WhatsAppNotification).filter(
            WhatsAppNotification.email_log_id == email_log.id
        ).count()
        print(f"   📱 WhatsApp records in DB: {whatsapp_count}")
        
        # Check SMS notifications
        sms_notifications = db.query(SMSNotification).filter(
            SMSNotification.email_log_id == email_log.id
        ).all()
        print(f"   📨 SMS records in DB: {len(sms_notifications)}")
        
        for sms in sms_notifications:
            status_icon = "✅" if sms.status == 'sent' else "❌"
            print(f"      {status_icon} {sms.employee_name} ({sms.recipient}) - {sms.status}")
            if sms.message_id:
                print(f"         Message ID: {sms.message_id}")
            if sms.error_message:
                print(f"         Error: {sms.error_message}")
        
        # Check Voice calls
        voice_count = db.query(VoiceCall).filter(
            VoiceCall.email_log_id == email_log.id
        ).count()
        print(f"   📞 Voice call records in DB: {voice_count}")
        
    except Exception as e:
        print(f"❌ Database check error: {str(e)}")

def main():
    """Main test function"""
    print("🚀 Testing Full Email Processing Pipeline")
    print("=" * 60)
    
    # Step 1: Create test email
    email_log, db = create_test_email()
    
    # Step 2: AI processing (required for notifications)
    ai_success = test_ai_processing(email_log, db)
    
    if not ai_success:
        print("\n❌ AI processing failed, cannot test notifications")
        return
    
    # Step 3: Test notification service
    notification_results = test_notification_service(email_log, db)
    
    # Step 4: Check database records
    check_database_records(email_log, db)
    
    # Summary
    print(f"\n🎯 Test Summary:")
    print(f"   📧 Email created: ✅")
    print(f"   🤖 AI processing: {'✅' if ai_success else '❌'}")
    print(f"   📱 Notifications sent: {'✅' if notification_results else '❌'}")
    
    sms_count = len(notification_results.get('sms_notifications', []))
    whatsapp_count = len(notification_results.get('whatsapp_notifications', []))
    
    print(f"   📨 SMS notifications: {sms_count}")
    print(f"   📱 WhatsApp notifications: {whatsapp_count}")
    
    if sms_count > 0:
        print(f"\n🎉 SMS notifications are working in the pipeline!")
    else:
        print(f"\n⚠️  SMS notifications are not being sent in the pipeline!")
        print(f"   💡 This could be due to:")
        print(f"      • SMS service not properly initialized")
        print(f"      • Configuration issues")
        print(f"      • Employee data not being fetched correctly")
    
    print(f"\n💡 Next Steps:")
    print(f"   • Check your phones for received messages")
    print(f"   • Send a real email to trigger the pipeline")
    print(f"   • Check the dashboard for updated statistics")

if __name__ == "__main__":
    main()
