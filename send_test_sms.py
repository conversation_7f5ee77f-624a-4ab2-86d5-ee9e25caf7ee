#!/usr/bin/env python3
"""
Script to send test SMS to all management employees
"""

import sys
import os
import requests
import time
sys.path.append('.')

from app.models import EmailLog, SMSNotification, get_db
from app.worker import get_config
from app.services.vonage_sms import get_sms_service

def send_test_sms_to_all():
    """Send test SMS to all management employees"""
    print("📱 Sending Test SMS to All Management Employees")
    print("=" * 60)
    
    try:
        # Get configuration and SMS service
        config = get_config()
        sms_service = get_sms_service(config)
        db = get_db()
        
        print(f"SMS Service: {type(sms_service).__name__}")
        print(f"SMS Enabled: {config.get('sms_enabled')}")
        
        # Create a test email log for context
        test_email = EmailLog(
            message_id=f"manual-sms-test-{int(time.time())}",
            sender="<EMAIL>",
            recipient="<EMAIL>",
            subject="Manual SMS Test",
            whatsapp_summary="This is a manual test SMS sent to all management employees to verify SMS functionality.",
            status="processed"
        )
        
        db.add(test_email)
        db.commit()
        print(f"✅ Created test email context with ID: {test_email.id}")
        
        # Send SMS notifications to all employees
        print("\n📤 Sending SMS notifications...")
        sms_notifications = sms_service.send_notifications(test_email, db)
        
        print(f"\n📊 SMS Results:")
        print(f"Total SMS sent: {len(sms_notifications)}")
        
        success_count = 0
        failed_count = 0
        
        for notification in sms_notifications:
            if notification.status == 'sent':
                status_icon = "✅"
                success_count += 1
            else:
                status_icon = "❌"
                failed_count += 1
                
            print(f"\n{status_icon} {notification.employee_name}")
            print(f"   📞 Phone: {notification.recipient}")
            print(f"   📋 Status: {notification.status}")
            print(f"   📝 Message: {notification.message[:50]}...")
            
            if notification.message_id:
                print(f"   🆔 Message ID: {notification.message_id}")
            if notification.error_message:
                print(f"   ❌ Error: {notification.error_message}")
            if notification.sent_at:
                print(f"   ⏰ Sent at: {notification.sent_at}")
        
        print(f"\n📈 Summary:")
        print(f"   ✅ Successful: {success_count}")
        print(f"   ❌ Failed: {failed_count}")
        print(f"   📊 Total: {len(sms_notifications)}")
        
        return sms_notifications
        
    except Exception as e:
        print(f"❌ Error sending SMS: {str(e)}")
        import traceback
        traceback.print_exc()
        return []

def send_individual_sms(phone_number, employee_name, custom_message=None):
    """Send SMS to a specific employee"""
    print(f"\n📱 Sending SMS to {employee_name} ({phone_number})")
    
    try:
        config = get_config()
        sms_service = get_sms_service(config)
        db = get_db()
        
        # Create test email context
        test_email = EmailLog(
            message_id=f"individual-sms-{employee_name.lower().replace(' ', '-')}-{int(time.time())}",
            sender="<EMAIL>",
            recipient="<EMAIL>",
            subject=f"Individual SMS Test for {employee_name}",
            whatsapp_summary=custom_message or f"Individual SMS test for {employee_name}",
            status="processed"
        )
        
        db.add(test_email)
        db.commit()
        
        # Send individual SMS
        result = sms_service.send_sms(
            recipient_phone=phone_number,
            message=custom_message or f"Hello {employee_name}, this is a test SMS from the Email Monitor system.",
            email_log=test_email,
            employee_name=employee_name
        )
        
        if result['success']:
            print(f"   ✅ SMS sent successfully!")
            print(f"   🆔 Message ID: {result.get('message_id')}")
            print(f"   📋 Status: {result.get('status')}")
        else:
            print(f"   ❌ SMS failed!")
            print(f"   📋 Error: {result.get('error')}")
        
        return result
        
    except Exception as e:
        print(f"   ❌ Error: {str(e)}")
        return {'success': False, 'error': str(e)}

def check_sms_via_api():
    """Check SMS notifications via API"""
    print("\n🔍 Checking SMS via API...")
    
    try:
        response = requests.get("http://localhost:8000/api/sms-notifications?limit=10", timeout=10)
        
        if response.status_code == 200:
            sms_list = response.json()
            print(f"   📊 Recent SMS notifications: {len(sms_list)}")
            
            for sms in sms_list[:5]:  # Show last 5
                status_icon = "✅" if sms['status'] == 'sent' else "❌"
                print(f"   {status_icon} {sms['employee_name']} ({sms['recipient']})")
                print(f"      Status: {sms['status']}")
                if sms['sent_at']:
                    print(f"      Sent: {sms['sent_at']}")
        else:
            print(f"   ❌ API Error: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ API Error: {str(e)}")

def get_employees_from_dashboard():
    """Fetch current employees from the dashboard API"""
    try:
        response = requests.get("http://localhost:8000/api/employees", timeout=10)
        if response.status_code == 200:
            employees = response.json()
            active_employees = [emp for emp in employees if emp.get('status') == 'active']
            return active_employees
        else:
            print(f"   ❌ API Error: {response.status_code}")
            return []
    except Exception as e:
        print(f"   ❌ API Error: {str(e)}")
        return []

def main():
    """Main function"""
    print("🚀 SMS Test for Management Employees")
    print("=" * 60)

    # Fetch current employees from dashboard
    print("📡 Fetching employees from dashboard...")
    employees = get_employees_from_dashboard()

    if not employees:
        print("❌ No employees found! Please check the dashboard.")
        return

    print(f"✅ Found {len(employees)} active employees:")
    for emp in employees:
        print(f"   • {emp['name']} - {emp['phone']} (ID: {emp['id']})")

    print("\n" + "=" * 60)
    
    # Option 1: Send to all employees
    print("\n🎯 Option 1: Send SMS to ALL employees")
    sms_results = send_test_sms_to_all()
    
    # Option 2: Send individual SMS (example to first employee)
    if employees:
        first_employee = employees[0]
        print(f"\n🎯 Option 2: Send individual SMS to {first_employee['name']}")
        individual_result = send_individual_sms(
            first_employee['phone'],
            first_employee['name'],
            f"Hello {first_employee['name']}! This is a personalized test SMS from the Email Monitor system. SMS integration is working perfectly!"
        )
    
    # Check via API
    check_sms_via_api()
    
    print("\n🎉 SMS Test Complete!")
    print("\n💡 Next Steps:")
    print("   • Check your phone for received SMS messages")
    print("   • Refresh the dashboard to see updated SMS statistics")
    print("   • SMS will be automatically sent when emails are processed")

if __name__ == "__main__":
    main()
