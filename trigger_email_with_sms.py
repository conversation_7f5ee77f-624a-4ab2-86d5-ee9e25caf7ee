#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to trigger email processing and demonstrate SMS functionality
"""

import sys
import os
import requests
import time
sys.path.append('.')

from app.models import EmailLog, get_db
from app.worker import process_email_pipeline

def create_test_email():
    """Create a test email in the database"""
    print("📧 Creating test email...")
    
    db = get_db()
    
    test_email = EmailLog(
        message_id=f"test-email-sms-{int(time.time())}",
        sender="<EMAIL>",
        recipient="<EMAIL>",
        subject="Urgent: Customer Support Request",
        body="Hello, I need immediate assistance with my account. This is an urgent matter that requires management attention.",
        status="pending"
    )
    
    db.add(test_email)
    db.commit()
    
    print(f"✅ Created test email with ID: {test_email.id}")
    return test_email.id

def trigger_email_processing(email_id):
    """Trigger the email processing pipeline"""
    print(f"🔄 Processing email ID: {email_id}")
    
    db = get_db()
    
    try:
        # Process the email through the pipeline
        process_email_pipeline(email_id, db)
        print("✅ Email processing completed!")
        
        # Get the processed email with all notifications
        email = db.query(EmailLog).filter(EmailLog.id == email_id).first()
        
        if email:
            print(f"\n📊 Processing Results:")
            print(f"  • Email Status: {email.status}")
            print(f"  • WhatsApp Notifications: {len(email.notifications)}")
            print(f"  • SMS Notifications: {len(email.sms_notifications)}")
            print(f"  • Email Replies: {len(email.replies)}")
            
            # Show SMS details
            if email.sms_notifications:
                print(f"\n📱 SMS Notifications Sent:")
                for sms in email.sms_notifications:
                    status_icon = "✅" if sms.status == "sent" else "❌"
                    print(f"  {status_icon} {sms.employee_name} ({sms.recipient}): {sms.status}")
                    if sms.message_id:
                        print(f"    Message ID: {sms.message_id}")
                    if sms.error_message:
                        print(f"    Error: {sms.error_message}")
            
            # Show WhatsApp details
            if email.notifications:
                print(f"\n💬 WhatsApp Notifications Sent:")
                for wa in email.notifications:
                    status_icon = "✅" if wa.status == "sent" else "❌"
                    print(f"  {status_icon} {wa.recipient}: {wa.status}")
        
        return email
        
    except Exception as e:
        print(f"❌ Error processing email: {str(e)}")
        import traceback
        traceback.print_exc()
        return None

def check_api_stats():
    """Check the API stats to see SMS counts"""
    print("\n📊 Checking API Statistics...")
    
    try:
        response = requests.get("http://localhost:8000/api/stats", timeout=10)
        
        if response.status_code == 200:
            stats = response.json()
            print(f"  • Total Emails: {stats['emails']['total']}")
            print(f"  • WhatsApp Notifications: {stats['notifications']['total']}")
            print(f"  • SMS Notifications: {stats['sms_notifications']['total']}")
            print(f"  • Email Replies: {stats['replies']['total']}")
            
            return stats
        else:
            print(f"❌ API request failed: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error checking API stats: {str(e)}")
        return None

def check_sms_notifications():
    """Check SMS notifications via API"""
    print("\n📱 Checking SMS Notifications via API...")
    
    try:
        response = requests.get("http://localhost:8000/api/sms-notifications", timeout=10)
        
        if response.status_code == 200:
            sms_notifications = response.json()
            print(f"  • Total SMS Notifications: {len(sms_notifications)}")
            
            if sms_notifications:
                print("  • Recent SMS Notifications:")
                for sms in sms_notifications[:5]:  # Show last 5
                    status_icon = "✅" if sms['status'] == "sent" else "❌"
                    print(f"    {status_icon} {sms['employee_name']} ({sms['recipient']}): {sms['status']}")
                    if sms['sent_at']:
                        print(f"      Sent: {sms['sent_at']}")
            
            return sms_notifications
        else:
            print(f"❌ API request failed: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ Error checking SMS notifications: {str(e)}")
        return None

def main():
    """Main function to demonstrate SMS functionality"""
    print("🚀 Email Processing with SMS Demo")
    print("=" * 50)
    
    # Check initial stats
    print("📊 Initial Statistics:")
    initial_stats = check_api_stats()
    
    # Create and process test email
    email_id = create_test_email()
    
    if email_id:
        # Process the email (this should trigger SMS)
        processed_email = trigger_email_processing(email_id)
        
        if processed_email:
            # Check updated stats
            print("\n📊 Updated Statistics:")
            final_stats = check_api_stats()
            
            # Check SMS notifications
            check_sms_notifications()
            
            # Show the difference
            if initial_stats and final_stats:
                sms_diff = final_stats['sms_notifications']['total'] - initial_stats['sms_notifications']['total']
                wa_diff = final_stats['notifications']['total'] - initial_stats['notifications']['total']
                
                print(f"\n📈 Changes:")
                print(f"  • New SMS Notifications: +{sms_diff}")
                print(f"  • New WhatsApp Notifications: +{wa_diff}")
    
    print("\n🎉 Demo Complete!")
    print("\n💡 Tips:")
    print("  • Check the dashboard at http://localhost:8000/dashboard")
    print("  • SMS statistics should now be updated")
    print("  • Look for SMS badges in the email activity feed")

if __name__ == "__main__":
    main()
